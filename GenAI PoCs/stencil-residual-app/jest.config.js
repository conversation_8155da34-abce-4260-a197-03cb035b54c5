module.exports = {
  preset: '@stencil/core/testing',
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  transformIgnorePatterns: [
    '/node_modules/(?!@stencil)'
  ],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/www/'
  ],
  setupFilesAfterEnv: ['./setupJest.ts'],
  moduleFileExtensions: [
    'ts',
    'tsx',
    'js'
  ],
  // Fix the module mapper syntax
  moduleNameMapper: {
    "^@stencil/core$": "<rootDir>/node_modules/@stencil/core/testing"
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/components.d.ts',
    '!src/index.ts',
    '!src/stencil-core.d.ts'
  ],
  globals: {
    'ts-jest': {
      diagnostics: false,
      isolatedModules: true,
      tsconfig: 'tsconfig.json'
    }
  },
  testRunner: "jest-jasmine2"
}
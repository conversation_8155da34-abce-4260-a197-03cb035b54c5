# Residuals Entry StencilJS Application

This is a modern StencilJS implementation of the Residuals Entry application. It maintains the same UI and functionality as the original PHP application while providing a modern component-based architecture.

## Features

- Responsive UI that matches the original PHP application
- Component-based architecture using StencilJS
- Mock data for development and testing
- "Select a Residual Master Record" as the landing page

## Getting Started

### Prerequisites

- Node.js (v14+)
- npm or yarn

### Installation

1. Clone the repository
2. Navigate to the project directory:
   ```
   cd stencil-residual-app
   ```
3. Install dependencies:
   ```
   npm install
   ```

### Development

To start the development server:

```
npm start
```

This will start a development server at http://localhost:3333

### Building for Production

To build the application for production:

```
npm run build
```

The build output will be in the `www` directory.

## Project Structure

```
stencil-residual-app/
├── src/
│   ├── components/
│   │   ├── app-root/             # Main app component and router
│   │   ├── navbar/               # Navigation bar component
│   │   ├── common/
│   │   │   ├── message-display/  # Message display component
│   │   ├── residual-master-selection/ # Landing page component
│   │   ├── residual-master-detail/    # Detail view component
│   │   ├── residual-settings/         # Settings component
│   ├── services/
│   │   ├── data.service.ts       # Mock data service
│   ├── models/
│   │   ├── residual-master.model.ts  # Data models
│   ├── utils/
│   │   ├── date-helper.ts        # Date formatting utilities
│   ├── global/
│   │   ├── app.css               # Global styles
│   ├── index.html                # Main HTML entry point
├── stencil.config.ts             # StencilJS configuration
├── package.json                  # Dependencies and scripts
```

## Future Enhancements

- Connect to a real backend API instead of using mock data
- Add form validation for data entry
- Implement search and filtering capabilities
- Add unit and integration tests
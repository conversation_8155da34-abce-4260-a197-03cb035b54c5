import { Component, h, State } from '@stencil/core';
import { dataService } from '../../services/data.service';
import { ResidualAdjustment, ResidualMaster, ResidualValue, ResidualVehicleGroup } from '../../models/residual-master.model';
import { formatDate } from '../../utils/date-helper';

interface ResidualVehicle {
  id: number;
  residual_vehicle_group_id: number;
  vehicle_group_name: string;
  msrp: number;
  factory_invoice: number;
  term_values: { [key: number]: number };
  status: string;
  adjustments?: ResidualAdjustment[];
  division: string;
}

// interface ResidualAdjustment {
//   id: number;
//   residual_eq_adjust_code_id: number;
//   description: string;
//   terms: { [key: number]: number };
//   mrm_adjustment?: number;
// }

@Component({
  tag: 'residual-master-detail',
  styleUrl: 'residual-master-detail.css',
  shadow: true,
})
export class ResidualMasterDetail {
  @State() residualMaster: ResidualMaster;
  @State() error: string = '';
  @State() message: string = '';
  @State() loading: boolean = true;
  @State() isEditMode: boolean = false;
  @State() residualValues: ResidualValue[] = [];
  @State() residualVehicles: ResidualVehicle[] = [];
  @State() selectedTerm: number = 24;
  @State() selectedMileage: number = 12000;
  @State() newValueInput: string = '';
  @State() activeTab: string = 'vehicles';
  @State() startRowNumber: number = 0;
  @State() numberOfRowsToShow: number = 20;
  
  // Available terms, mileages, and models for the selected residual master
  @State() availableTerms: number[]= [];
  @State() availableMileages: number[] = [7500, 10000, 12000, 15000, 18000, 20000];
  @State() adjustmentCodes: { [key: string]: any } = {
    Add: {
      Absent: {},
      Present: {}
    },
    Deduct: {
      Absent: {},
      Present: {}
    }
  };
  @State() masterVeichleGroups: ResidualVehicleGroup[] = [];

  async componentWillLoad() {
    // Get the residual master ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const resMastID = urlParams.get('ResMastID');
    
    if (resMastID) {
      try {
        this.loading = true;
        // Load the residual master data using await
        const residualMaster = await dataService.getResidualMasterById(parseInt(resMastID, 10));
        console.log('residual_master');
        console.log(residualMaster);
        if (!residualMaster) {
          this.error = 'Residual master record not found';
        } else {
          this.residualMaster = residualMaster;
          this.availableTerms = residualMaster.residualMasterTermTemplates.map(c=>c.programTerm.pgmTermMin);
          this.masterVeichleGroups = await dataService.getVehicleGroupsForResidualMaster(parseInt(resMastID, 10));
          // Generate mock residual values and vehicles
          this.generateMockResidualValues(residualMaster);
          this.generateMockResidualVehicles(residualMaster);
          this.generateMockAdjustmentCodes();
        }
      } catch (err) {
        this.error = 'Error loading residual master data';
        console.error(err);
      }
      finally {
        this.loading = false;
      }
    } else {
      this.error = 'No residual master ID specified';
      this.loading = false;
    }
      }

  // Add navigation method
  navigate(path: string, event?: Event) {
    if (event) {
      event.preventDefault();
    }
    
    // Change the URL in the address bar
    window.history.pushState({}, '', path);
    
    // Dispatch a custom event with all the route data for app-root to handle
    const navEvent = new CustomEvent('navchange', {
      detail: { path },
      bubbles: true,
      composed: true
    });
    window.dispatchEvent(navEvent);
  }
  
  // Generate mock residual values for demonstration
  generateMockResidualValues(masterData: ResidualMaster) {
    // const values: ResidualValue[] = [];
    // let id = 1;
    
    // this.availableTerms.forEach(term => {
    //   this.availableMileages.forEach(mileage => {
    //     // Generate a realistic residual value (higher for shorter terms and lower mileages)
    //     const baseValue = 80 - (term / 12 * 5) - (mileage / 5000);
    //     const value = Math.max(40, Math.min(80, baseValue));
        
    //     values.push({
    //       id: id++,
    //       term,
    //       mileage, 
    //       value,
    //       isEditing: false
    //     });
    //   });
    // });
    var x = masterData.residualVehicleGroupData;
    const values: ResidualValue[] = [];
    masterData.residualMasterTermTemplates.forEach(termObject => {
       this.availableMileages.forEach(mileage => {
        const value = x[0].residualValues[0].value;
        const term = termObject.programTerm.pgmTermMin;
        const pgTermId = termObject.pgmTermID;
        values.push({
                id: 12,
                term,
                mileage, 
                value,
                isEditing: false,
                pgmTermID: pgTermId,
                value_type: 0
              });
       });
      });
    this.residualValues = values;
  }
  
  // Generate mock residual vehicles
  generateMockResidualVehicles(masterData: ResidualMaster) {
    const vehicles: ResidualVehicle[] = [];
    let id = 1;
    
    // Create mock vehicle data that resembles the legacy system
    const veichleGroupsNew: {
      residualVehicleGroup: ResidualVehicleGroup,
      msrp: number,
      factory_invoice: number,
      residualValues: ResidualValue[],
      residualDataAdjustments: ResidualAdjustment[]
    }[] = masterData.residualVehicleGroupData.map(c=> {
      return {
        residualVehicleGroup: c.residualVehicleGroup,
        msrp: c.msrp,
        factory_invoice: c.factory_invoice,
        residualValues: c.residualValues,
        residualDataAdjustments: c.residualDataAdjustments
      }
    }
  );
    // const vehicleGroups = [
    //   { id: 1, name: 'Camry LE 2.5L' },
    //   { id: 2, name: 'Camry SE 2.5L' },
    //   { id: 3, name: 'Camry XLE 2.5L' },
    //   { id: 4, name: 'Camry XSE 2.5L' },
    //   { id: 5, name: 'RAV4 LE AWD' },
    //   { id: 6, name: 'RAV4 XLE AWD' },
    //   { id: 7, name: 'Corolla LE' },
    //   { id: 8, name: 'Corolla XLE' },
    //   { id: 9, name: 'Highlander LE AWD' },
    //   { id: 10, name: 'Highlander XLE AWD' }
    // ];
    
    veichleGroupsNew.forEach(group => {
      // Generate term values for each term
      const termValues = {};
      masterData.residualMasterTermTemplates.forEach(term => {
        // Generate a realistic residual value (higher for shorter terms)
        termValues[term.programTerm.pgmTermMin] = group.residualValues.find(c=>c.pgmTermID == term.pgmTermID)?.value
      });
      
      const vehicle: ResidualVehicle = {
        id: id++,
        residual_vehicle_group_id: group.residualVehicleGroup.residual_vehicle_group_id,
        vehicle_group_name: group.residualVehicleGroup.vehicle_group_name,
        msrp: group.msrp,
        factory_invoice: group.factory_invoice,
        term_values: termValues,
        status: 'Active',
        adjustments: group.residualDataAdjustments,
        division: group.residualVehicleGroup.division.divName
      };
      
      // Add some adjustments to some vehicles
      // if (Math.random() > 0.5) {
      //   const adjustmentCount = Math.floor(Math.random() * 3) + 1;
      //   for (let i = 0; i < adjustmentCount; i++) {
      //     const adjTermValues = {};
      //     this.availableTerms.forEach(term => {
      //       adjTermValues[term] = Math.floor(Math.random() * 3) + 1;
      //     });
          
      //     vehicle.adjustments.push({
      //       id: i + 1,
      //       residual_eq_adjust_code_id: Math.floor(Math.random() * 10) + 1,
      //       description: `Adjustment for ${['Sunroof', 'Navigation', 'Premium Package', 'Technology Package'][Math.floor(Math.random() * 4)]}`,
      //       terms: adjTermValues,
      //       mrm_adjustment: Math.floor(Math.random() * 500) + 100
      //     });
      //   }
      // }
      
      vehicles.push(vehicle);
    });
    
    this.residualVehicles = vehicles;
  }
  
  // Generate mock adjustment codes
  generateMockAdjustmentCodes() {
    const addAbsent = {
      1: 'Add Sunroof',
      2: 'Add Navigation',
      3: 'Add Premium Package',
      4: 'Add Technology Package'
    };
    
    const addPresent = {
      5: 'Add Sport Package',
      6: 'Add Leather Seats',
      7: 'Add Appearance Package'
    };
    
    const deductAbsent = {
      8: 'Remove Standard Wheels',
      9: 'Remove Standard Audio',
      10: 'Remove Standard Interior'
    };
    
    const deductPresent = {
      11: 'Remove Optional Wheels',
      12: 'Remove Optional Audio',
      13: 'Remove Optional Interior'
    };
    
    this.adjustmentCodes = {
      Add: {
        Absent: addAbsent,
        Present: addPresent
      },
      Deduct: {
        Absent: deductAbsent,
        Present: deductPresent
      }
    };
  }
  
  // Toggle edit mode
  toggleEditMode() {
    this.isEditMode = !this.isEditMode;
  }
  
  // Handle term selection
  handleTermChange(event: Event) {
    const select = event.target as HTMLSelectElement;
    this.selectedTerm = parseInt(select.value, 10);
  }
  
  // Handle mileage selection
  handleMileageChange(event: Event) {
    const select = event.target as HTMLSelectElement;
    this.selectedMileage = parseInt(select.value, 10);
  }
  
  // Start editing a specific residual value
  editResidualValue(vehicleId: number, _term: number) {
    if (!this.isEditMode) return;
    
    // Find the vehicle
    const vehicleIndex = this.residualVehicles.findIndex(v => v.id === vehicleId);
    if (vehicleIndex === -1) return;
    
    // Set the current vehicle to "Modified" status
    const updatedVehicles = [...this.residualVehicles];
    updatedVehicles[vehicleIndex] = {
      ...updatedVehicles[vehicleIndex],
      status: 'Modified'
    };
    
    this.residualVehicles = updatedVehicles;
  }
  
  // Update a residual value
  updateResidualValue(vehicleId: number, term: number, newValue: string) {
    if (!newValue) return;
    
    const value = parseFloat(newValue);
    if (isNaN(value) || value < 0 || value > 100) {
      this.error = 'Residual value must be between 0 and 100';
      return;
    }
    
    // Find the vehicle
    const vehicleIndex = this.residualVehicles.findIndex(v => v.id === vehicleId);
    if (vehicleIndex === -1) return;
    
    // Update the term value
    const updatedVehicles = [...this.residualVehicles];
    updatedVehicles[vehicleIndex] = {
      ...updatedVehicles[vehicleIndex],
      term_values: {
        ...updatedVehicles[vehicleIndex].term_values,
        [term]: value
      },
      status: 'Modified'
    };
    
    this.residualVehicles = updatedVehicles;
    this.message = 'Residual value updated successfully';
    setTimeout(() => this.message = '', 3000);
  }
  
  // Add a new vehicle row
  addNewVehicleRow() {
    // Generate empty term values
    const termValues = {};
    this.availableTerms.forEach(term => {
      termValues[term] = 0;
    });
    
    const newVehicle: ResidualVehicle = {
      id: this.residualVehicles.length + 1,
      residual_vehicle_group_id: 0,
      vehicle_group_name: '',
      msrp: 0,
      factory_invoice: 0,
      term_values: termValues,
      status: 'New',
      adjustments: [],
      division: ''
    };
    
    this.residualVehicles = [...this.residualVehicles, newVehicle];
  }
  
  // Add adjustment row to a vehicle
  addAdjustmentRow(vehicleId: number) {
    // Find the vehicle
    const vehicleIndex = this.residualVehicles.findIndex(v => v.id === vehicleId);
    if (vehicleIndex === -1) return;
    
    // Create empty terms object
    const terms = {};
    this.availableTerms.forEach(term => {
      terms[term] = 0;
    });
    
    // Create new adjustment
    const newAdjustment: ResidualAdjustment = {
      residual_data_adjustment_id: (this.residualVehicles[vehicleIndex].adjustments?.length || 0) + 1,
      ResidualAdjustCode: 0,
      terms: terms,
      msrp: 0,
      mrm: '',
      adjustment_value: 0,
      pgmTermID:0,
      ResidualVehicleGroupDataID: 0
    };
    
    // Add adjustment to vehicle
    const updatedVehicles = [...this.residualVehicles];
    updatedVehicles[vehicleIndex] = {
      ...updatedVehicles[vehicleIndex],
      adjustments: [...(updatedVehicles[vehicleIndex].adjustments || []), newAdjustment],
      status: 'Modified'
    };
    
    this.residualVehicles = updatedVehicles;
  }
  
  // Delete an adjustment row
  deleteAdjustmentRow(vehicleId: number, adjustmentId: number) {
    // Find the vehicle
    const vehicleIndex = this.residualVehicles.findIndex(v => v.id === vehicleId);
    if (vehicleIndex === -1) return;
    
    // Remove the adjustment
    const updatedVehicles = [...this.residualVehicles];
    updatedVehicles[vehicleIndex] = {
      ...updatedVehicles[vehicleIndex],
      adjustments: updatedVehicles[vehicleIndex].adjustments?.filter(a => a.residual_data_adjustment_id !== adjustmentId) || [],
      status: 'Modified'
    };
    
    this.residualVehicles = updatedVehicles;
  }
  
  // Delete a vehicle row
  deleteVehicleRow(vehicleId: number) {
    this.residualVehicles = this.residualVehicles.filter(v => v.id !== vehicleId);
  }
  
// Save all changes
async saveAllChanges() {
  try {
    // Set loading state while saving
    this.loading = true;
    this.error = '';
    
    // Only send vehicles that have been modified
    const vehiclesToSave = this.residualVehicles.filter(v => 
      v.status === 'Modified' || v.status === 'New'
    );
    
    if (vehiclesToSave.length === 0) {
      this.message = 'No changes to save';
      this.loading = false;
      setTimeout(() => this.message = '', 3000);
      return;
    }
    
    // Call the API to save the changes
    const success = await dataService.saveResidualVehicles(
      this.residualMaster.res_mast_id, 
      vehiclesToSave
    );
    
    if (success) {
      this.message = 'All changes saved successfully';
      
      // Reset all vehicles status to 'Active'
      this.residualVehicles = this.residualVehicles.map(v => ({
        ...v,
        status: 'Active'
      }));
    } else {
      this.error = 'Failed to save changes. Please try again.';
    }
  } catch (error) {
    this.error = `Error saving changes: ${error.message || 'Unknown error'}`;
  } finally {
    this.loading = false;
    setTimeout(() => {
      if (this.message) this.message = '';
      if (this.error) this.error = '';
    }, 3000);
  }
}
  
  // Make all rows active
  makeAllRowsActive() {
    // This would normally send data to the server
    this.message = 'All rows set to Active status';
    
    // Reset all vehicles status to 'Active'
    this.residualVehicles = this.residualVehicles.map(v => ({
      ...v,
      status: 'Active'
    }));
    
    setTimeout(() => this.message = '', 3000);
  }
  
  // Update vehicle group
  updateVehicleGroup(vehicleId: number, newGroupId: number, newGroupName: string) {
    // Find the vehicle
    const vehicleIndex = this.residualVehicles.findIndex(v => v.id === vehicleId);
    if (vehicleIndex === -1) return;
    
    // Update the vehicle group
    const updatedVehicles = [...this.residualVehicles];
    updatedVehicles[vehicleIndex] = {
      ...updatedVehicles[vehicleIndex],
      residual_vehicle_group_id: newGroupId,
      vehicle_group_name: newGroupName,
      status: 'Modified'
    };
    
    this.residualVehicles = updatedVehicles;
  }
  
  // Render the vehicles table
  renderVehiclesTable() {
    return (
      <div class="residual-vehicles-container">
        <table id="tableResiduals" class="Residuals">
          <thead>
            <tr class="headerRow">
              <th class="status-col">Status</th>
              <th class="status-col">Division</th>
              <th class="vehicle-col">Vehicle</th>
              <th class="msrp-col">MSRP</th>
              <th class="invoice-col">Invoice</th>
              {this.availableTerms.map(term => (
                <th class="term-col">{term} mo</th>
              ))}
              {this.residualMaster.data_source !== 'ALG' && (
                <th class="actions-col">Actions</th>
              )}
            </tr>
          </thead>
          <tbody>
            {this.residualVehicles.map((vehicle, rowIndex) => [
              <tr class={rowIndex % 2 === 0 ? 'rowEven' : 'rowOdd'} id={`VehicleRow_${rowIndex + 1}`}>
                <td class="status-col">
                  <span class={`status-${vehicle.status.toLowerCase()}`}>{vehicle.status}</span>
                </td>
                <td class="vehicle-col">
                {vehicle.division}
                </td>
                <td class="vehicle-col">
                  {vehicle.status === 'New' ? (
                   <select 
                   value={vehicle.residual_vehicle_group_id ? `${vehicle.residual_vehicle_group_id}||${vehicle.vehicle_group_name}` : ''}
                   onChange={(e: Event) => {
                     const select = e.target as HTMLSelectElement;
                     if (select.value) {
                       const [idStr, name] = select.value.split('||');
                       const id = parseInt(idStr, 10);
                       this.updateVehicleGroup(vehicle.id, id, name);
                     }
                   }}
                 >
                   <option value="">Select Vehicle Group</option>
                   {this.masterVeichleGroups.map(group => (
                     <option value={`${group.residual_vehicle_group_id}||${group.vehicle_group_name}`}>{group.vehicle_group_name}</option>
                   ))}
                 </select>

                  ) : (
                    vehicle.vehicle_group_name
                  )}
                </td>
                <td class="msrp-col">
                  <input 
                    type="number" 
                    min="0" 
                    value={vehicle.msrp.toString()} 
                    onChange={(e: Event) => {
                      const input = e.target as HTMLInputElement;
                      const updatedVehicles = [...this.residualVehicles];
                      updatedVehicles[rowIndex].msrp = parseInt(input.value, 10) || 0;
                      updatedVehicles[rowIndex].status = 'Modified';
                      this.residualVehicles = updatedVehicles;
                    }}
                    disabled={this.residualMaster.data_source === 'ALG'}
                  />
                </td>
                <td class="invoice-col">
                  <input 
                    type="number" 
                    min="0" 
                    value={vehicle.factory_invoice.toString()} 
                    onChange={(e: Event) => {
                      const input = e.target as HTMLInputElement;
                      const updatedVehicles = [...this.residualVehicles];
                      updatedVehicles[rowIndex].factory_invoice = parseInt(input.value, 10) || 0;
                      updatedVehicles[rowIndex].status = 'Modified';
                      this.residualVehicles = updatedVehicles;
                    }}
                    disabled={this.residualMaster.data_source === 'ALG'}
                  />
                </td>
                {this.availableTerms.map(term => (
                  <td class="term-col">
                    <input 
                      type="number" 
                      min="0" 
                      max="100" 
                      step="1" 
                      value={vehicle.term_values[term]?.toString() || '0'}
                      onChange={(e: Event) => {
                        const input = e.target as HTMLInputElement;
                        this.updateResidualValue(vehicle.id, term, input.value);
                      }}
                      disabled={this.residualMaster.data_source === 'ALG'}
                      class="term-input"
                    />
                  </td>
                ))}
                {this.residualMaster.data_source !== 'ALG' && (
                  <td class="actions-col">
                    <button 
                     type= "button"
                      class="action-button add-adj-btn"
                      onClick={() => this.addAdjustmentRow(vehicle.id)}
                      title="Add Adjustment"
                    >
                      + Adj
                    </button>
                    <button 
                     type= "button"
                      class="action-button delete-btn"
                      onClick={() => this.deleteVehicleRow(vehicle.id)}
                      title="Delete Vehicle Row"
                    >
                      Del
                    </button>
                  </td>
                )}
              </tr>,
              
              // Render adjustment rows if any
              // ...(vehicle.adjustments || []).map((adjustment, adjIndex) => (
              //   <tr 
              //     class={adjIndex % 2 === 0 ? 'adjustmentRowEven' : 'adjustmentRowOdd'} 
              //     id={`adjustmentRow_${rowIndex + 1}_${adjIndex + 1}`}
              //   >
              //     <td class="status-col"></td>
              //     <td class="vehicle-col adjustment-name">
              //       <select 
              //         class="adjustment-code-select"
              //         value={adjustment.ResidualAdjustCode ? adjustment.ResidualAdjustCode.toString() : 0}
              //         onChange={(e: Event) => {
              //           const select = e.target as HTMLSelectElement;
              //           const updatedVehicles = [...this.residualVehicles];
              //           const vehicleIdx = updatedVehicles.findIndex(v => v.id === vehicle.id);
              //           const adjustmentIdx = updatedVehicles[vehicleIdx].adjustments.findIndex(a => a.residual_data_adjustment_id === adjustment.residual_data_adjustment_id);
                        
              //           updatedVehicles[vehicleIdx].adjustments[adjustmentIdx].ResidualAdjustCode = parseInt(select.value, 10);
              //           updatedVehicles[vehicleIdx].status = 'Modified';
                        
              //           this.residualVehicles = updatedVehicles;
              //         }}
              //         disabled={this.residualMaster.data_source === 'ALG'}
              //       >
              //         <option value="0">Select Adjustment</option>
              //         <optgroup label="Add - Equipment Absent">
              //           {Object.entries(this.adjustmentCodes.Add.Absent).map(([id, name]) => (
              //             <option value={id}>{name}</option>
              //           ))}
              //         </optgroup>
              //         <optgroup label="Add - Equipment Present">
              //           {Object.entries(this.adjustmentCodes.Add.Present).map(([id, name]) => (
              //             <option value={id}>{name}</option>
              //           ))}
              //         </optgroup>
              //         <optgroup label="Deduct - Equipment Absent">
              //           {Object.entries(this.adjustmentCodes.Deduct.Absent).map(([id, name]) => (
              //             <option value={id}>{name}</option>
              //           ))}
              //         </optgroup>
              //         <optgroup label="Deduct - Equipment Present">
              //           {Object.entries(this.adjustmentCodes.Deduct.Present).map(([id, name]) => (
              //             <option value={id}>{name}</option>
              //           ))}
              //         </optgroup>
              //       </select>
                    
              //       <input 
              //         type="number"
              //         class="mrm-adjustment"
              //         placeholder="MRM Adjustment"
              //         value={adjustment.mrm?.toString() || ''}
              //         onChange={(e: Event) => {
              //           const input = e.target as HTMLInputElement;
              //           const updatedVehicles = [...this.residualVehicles];
              //           const vehicleIdx = updatedVehicles.findIndex(v => v.id === vehicle.id);
              //           const adjustmentIdx = updatedVehicles[vehicleIdx].adjustments.findIndex(a => a.residual_data_adjustment_id === adjustment.residual_data_adjustment_id);
                        
              //           updatedVehicles[vehicleIdx].adjustments[adjustmentIdx].mrm = input.value || undefined;
              //           updatedVehicles[vehicleIdx].status = 'Modified';
                        
              //           this.residualVehicles = updatedVehicles;
              //         }}
              //         disabled={this.residualMaster.data_source === 'ALG'}
              //       />
              //     </td>
              //     <td class="msrp-col"></td>
              //     <td class="invoice-col"></td>
              //     {this.availableTerms.map(term => (
              //       <td class="term-col">
              //         <input 
              //           type="number" 
              //           min="0" 
              //           max="100" 
              //           step="0.1" 
              //           // value={adjustment.terms[term]?.toString() || '0'}
              //           onChange={(e: Event) => {
              //             const input = e.target as HTMLInputElement;
              //             const updatedVehicles = [...this.residualVehicles];
              //             const vehicleIdx = updatedVehicles.findIndex(v => v.id === vehicle.id);
              //             const adjustmentIdx = updatedVehicles[vehicleIdx].adjustments.findIndex(a => a.residual_data_adjustment_id === adjustment.residual_data_adjustment_id);
                          
              //             updatedVehicles[vehicleIdx].adjustments[adjustmentIdx].terms[term] = parseFloat(input.value) || 0;
              //             updatedVehicles[vehicleIdx].status = 'Modified';
                          
              //             this.residualVehicles = updatedVehicles;
              //           }}
              //           disabled={this.residualMaster.data_source === 'ALG' || vehicle.term_values[term] === 0}
              //           class="term-input adjustment-input"
              //         />
              //       </td>
              //     ))}
              //     {this.residualMaster.data_source !== 'ALG' && (
              //       <td class="actions-col">
              //         <button 
              //         type= "button"
              //           class="action-button delete-btn"
              //           onClick={() => this.deleteAdjustmentRow(vehicle.id, adjustment.residual_data_adjustment_id)}
              //           title="Delete Adjustment"
              //         >
              //           Del
              //         </button>
              //       </td>
              //     )}
              //   </tr>
              // ))
            ])}
          </tbody>
        </table>
        
        {this.residualMaster.data_source !== 'ALG' && (
          <div class="button-container centered-buttons">
            <button type="button" class="SubmitButton" onClick={() => this.addNewVehicleRow()}>
              Add Another Vehicle
            </button>
            
            <button type="button" class="SubmitButton" onClick={() => this.saveAllChanges()}>
              Save Data
            </button>
            
            <button  type= "button" class="SubmitButton" onClick={() => this.makeAllRowsActive()}>
              Make All Rows ACTIVE
            </button>
          </div>
        )}
      </div>
    );
  }
  
  render() {
    if (this.loading) {
        return <app-loader text="Loading data..." fullScreen={true}></app-loader>;
    }

    if (this.error) {
      return (
        <div>
          <message-display messages={this.error} messageType="error"></message-display>
          <a href="/select-residual-master">Back to Residual Master Selection</a>
        </div>
      );
    }

    return (
      <div class="residual-detail-container">
        <center>
          <h2>Edit Residuals: {this.residualMaster.record_title}</h2>
          
          <message-display messages={this.error} messageType="error"></message-display>
          <message-display messages={this.message} messageType="message"></message-display>
          
          <form id="mainForm">
            {/* <input type="hidden" id="action" name="action" value="saveData" />
            <input type="hidden" name="Year" value={this.residualMaster.model_year} /> */}
            
            <table cellSpacing="0" cellPadding="0">
              <tr>
                <td bgcolor="#FFFFFF" style={{ padding: '1px' }} valign="top">
                  <table>
                    <tr>
                      <td>
                        <table>
                          <tr>
                            <td>Title</td>
                            <td><b>{this.residualMaster.record_title}</b></td>
                          </tr>
                          <tr>
                            <td>Data Source:</td>
                            <td><b>{this.residualMaster.data_source}</b></td>
                            <td>
                              <a 
                                style={{ color: '#0000ff', fontSize: '12px', fontWeight: 'bold', fontStyle: 'italic' }}
                                href={`/residual-master-settings?ResMastID=${this.residualMaster.res_mast_id}`}
                              >
                                Change Settings
                              </a>
                            </td>
                          </tr>
                          <tr>
                            <td>VehGrp Source</td>
                            <td><b>{this.residualMaster.vehicle_group_source || 'AIS'}</b></td>
                          </tr>
                          <tr>
                            <td>Year:</td>
                            <td><b>{this.residualMaster.model_year}</b></td>
                          </tr>
                          <tr>
                            <td>Finance Company:</td>
                            <td><b>{this.residualMaster.fiCoName || 'Not Specified'}</b></td>
                            <td>
                              <a 
                                style={{ color: '#0000ff', fontSize: '12px', fontWeight: 'bold', fontStyle: 'italic' }}
                                href={`/select-residual-master?DataSource=${this.residualMaster.data_source}&FiCoID=${this.residualMaster.fiCoID}`}
                              >
                                Change Residual Record
                              </a>
                            </td>
                          </tr>
                          <tr>
                            <td>Start Date:</td>
                            <td><b>{formatDate(this.residualMaster.startdate)}</b></td>
                          </tr>
                          <tr>
                            <td>Stop Date:</td>
                            <td><b>{formatDate(this.residualMaster.stopdate)}</b></td>
                          </tr>
                          <tr>
                            <td>ResMaster Status:</td>
                            <td>
                              <b>
                                {this.residualMaster.tblstatus_StatusID === 6 ? 'Pending' : 
                                 this.residualMaster.tblstatus_StatusID === 3 ? 'Active' : 'Archived'}
                              </b>
                            </td>
                            <td>
                              {this.residualMaster.tblstatus_StatusID === 3 ? (
                                <button 
                                 type= "button"
                                  class="SubmitButton"
                                  style={{ padding: '0px' }}
                                  onClick={() => {
                                    // This would update status in a real implementation
                                    this.message = 'Status changed to Pending';
                                    setTimeout(() => this.message = '', 3000);
                                  }}
                                >
                                  Make Pending
                                </button>
                              ) : (
                                <button 
                                 type= "button"
                                  class="SubmitButton"
                                  onClick={() => {
                                    // This would update status in a real implementation
                                    this.message = 'Status changed to Active';
                                    setTimeout(() => this.message = '', 3000);
                                  }}
                                >
                                  Make Active
                                </button>
                              )}
                            </td>
                          </tr>
                        </table>
                      </td>
                      
                      <td style={{ padding: '0px 0px 0px 70px' }}>
                        <div class="bulk-vehicle-container">
                          <h3>Bulk Vehicle Add</h3>
                          <select class="bulk-select">
                            <option value="">Select Bulk Action</option>
                            <option value="addAllToyota">Add All Toyota Models</option>
                            <option value="addAllLexus">Add All Lexus Models</option>
                            <option value="addSedans">Add All Sedans</option>
                            <option value="addSUVs">Add All SUVs</option>
                          </select>
                          <button  type= "button" class="SubmitButton" style={{ padding: '0px' }}>Add Selected</button>
                        </div>
                      </td>
                    </tr>
                  </table>
                  
                  <br />
                  
                  {this.renderVehiclesTable()}
                  
                </td>
              </tr>
            </table>
          </form>
          
          <div class="legend-container">
            <h4>Legend</h4>
            <ul class="legend-list">
              <li><span class="status-new">New</span> - New row, not yet saved</li>
              <li><span class="status-modified">Modified</span> - Modified row, not yet saved</li>
              <li><span class="status-active">Active</span> - Active row in database</li>
              <li><span class="status-pending">Pending</span> - Pending row in database</li>
            </ul>
          </div>
        </center>
      </div>
    );
  }
}
.residual-detail-container {
  font-family: Arial, Helvetica, sans-serif;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.residual-detail-container h2 {
  color: #003366;
  margin-top: 20px;
  margin-bottom: 20px;
}

.residual-detail-container table {
  border-collapse: collapse;
  width: 100%;
}

.residual-detail-container td {
  padding: 4px 8px;
}

.white-bg {
  background-color: #FFFFFF;
}

.content-cell {
  padding: 20px;
  text-align: left;
}

.residual-info {
  border: 1px solid blue;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #f8f9fa;
}

.residual-info h3 {
  margin-top: 0;
  color: #333;
  margin-bottom: 15px;
}

.detail-table {
  width: 100%;
  border-collapse: collapse;
}

.detail-table tr {
  border-bottom: 1px solid #eee;
}

.detail-table tr:last-child {
  border-bottom: none;
}

.detail-table td {
  padding: 6px 10px;
}

.detail-table .label {
  font-weight: bold;
  width: 25%;
  text-align: right;
  vertical-align: top;
}

.status-pending {
  color: orange;
  font-weight: bold;
  background-color: pink;
  padding: 3px 6px;
  border-radius: 3px;
}

.status-active {
  color: green;
  font-weight: bold;
  background-color: lightgreen;
  padding: 3px 6px;
  border-radius: 3px;
}

.status-archived {
  background-color: lightgray;
  padding: 3px 6px;
  border-radius: 3px;
}

.button-container {
  margin: 15px 0;
  text-align: center;
}

.submit-button {
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 8px 15px;
  margin: 0 10px;
  cursor: pointer;
  border-radius: 3px;
  font-size: 14px;
}

.submit-button:hover {
  background-color: #0052a3;
}

.submit-button.active {
  background-color: #cc3300;
}

.submit-button.active:hover {
  background-color: #aa2200;
}

/* Residual data container styles */
.residual-data-container {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 20px;
  margin-top: 20px;
  background-color: #f9f9f9;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ddd;
}

.view-tabs {
  display: flex;
}

.tab-button {
  padding: 8px 15px;
  margin-left: 10px;
  border: 1px solid #ccc;
  background-color: #f0f0f0;
  cursor: pointer;
  border-radius: 3px;
  font-size: 14px;
}

.tab-button:hover {
  background-color: #e0e0e0;
}

.tab-button.active {
  background-color: #0066cc;
  color: white;
  border-color: #0052a3;
}

.tab-content {
  margin-top: 15px;
}

.filter-controls {
  margin-bottom: 15px;
}

.filter-controls label {
  margin-right: 10px;
  font-weight: bold;
}

.filter-controls select {
  padding: 6px;
  border-radius: 3px;
  border: 1px solid #ccc;
  min-width: 150px;
}

/* Residual values table styles */
.residual-values-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.residual-values-table th {
  background-color: #f2f2f2;
  text-align: left;
  padding: 10px;
  border-bottom: 2px solid #ddd;
}

.residual-values-table td {
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

.residual-values-table tr:hover {
  background-color: #f5f5f5;
}

.residual-values-table tr.editing {
  background-color: #fffde7;
}

.editable {
  cursor: pointer;
  padding: 3px 6px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.editable:hover {
  background-color: #e0f7fa;
}

/* Action buttons for editing */
.edit-btn, .save-btn, .cancel-btn {
  padding: 5px 10px;
  margin: 0 5px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.edit-btn {
  background-color: #e0e0e0;
}

.save-btn {
  background-color: #4caf50;
  color: white;
}

.cancel-btn {
  background-color: #f44336;
  color: white;
}

.edit-actions {
  display: flex;
  justify-content: center;
}

/* Models display */
.models-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.model-card {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  width: calc(25% - 15px);
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.model-name {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.model-info {
  display: block;
  font-size: 12px;
  color: #666;
}

.Residuals {
  border: 1px solid #ccc;
  width: 100%;
  margin-top: 15px;
  margin-bottom: 15px;
}

.Residuals th {
  background-color: #003366;
  color: white;
  padding: 8px;
  text-align: center;
  font-size: 12px;
  position: sticky;
  top: 0;
}

.Residuals td {
  padding: 5px;
  text-align: center;
  font-size: 12px;
}

.rowEven {
  background-color: #f0f8ff;
}

.rowOdd {
  background-color: #ffffff;
}

.adjustmentRowEven {
  background-color: #fffaf0;
}

.adjustmentRowOdd {
  background-color: #fff5ee;
}

.adjustment-name {
  text-align: right;
  padding-right: 15px;
  font-style: italic;
}

.term-input {
  width: 50px;
  text-align: center;
}

.adjustment-input {
  color: #006400;
  font-weight: bold;
}

.centered-buttons {
  text-align: center;
}

.SubmitButton {
  background-color: #336699;
  color: white;
  border: none;
  padding: 6px 12px;
  margin: 0 5px;
  cursor: pointer;
  font-weight: bold;
  border-radius: 3px;
}

.SubmitButton:hover {
  background-color: #003366;
}

.action-button {
  background-color: #6699cc;
  color: white;
  border: none;
  padding: 2px 8px;
  margin: 0 2px;
  cursor: pointer;
  font-size: 11px;
  border-radius: 3px;
}

.action-button:hover {
  background-color: #336699;
}

.delete-btn {
  background-color: #cc6666;
}

.delete-btn:hover {
  background-color: #993333;
}

.vehicle-group-select, 
.adjustment-code-select {
  width: 100%;
  padding: 2px;
  font-size: 12px;
}

.mrm-adjustment {
  width: 80px;
  margin-left: 5px;
  font-size: 12px;
}

.status-modified {
  color: blue;
  font-weight: bold;
}

.status-new {
  color: purple;
  font-weight: bold;
}

.legend-container {
  margin-top: 20px;
  text-align: left;
  border: 1px solid #ccc;
  padding: 10px;
  background-color: #f9f9f9;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.legend-list {
  list-style-type: none;
  padding-left: 10px;
}

.legend-list li {
  margin-bottom: 5px;
}

.bulk-vehicle-container {
  border: 1px solid #ccc;
  padding: 10px;
  background-color: #f0f8ff;
  border-radius: 5px;
}

.bulk-vehicle-container h3 {
  margin-top: 0;
  color: #003366;
  font-size: 14px;
}

.bulk-select {
  width: 180px;
  padding: 3px;
  margin-right: 10px;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 1024px) {
  .residual-detail-container {
    padding: 10px;
  }
  
  .Residuals {
    display: block;
    overflow-x: auto;
  }
  
  .term-col {
    min-width: 60px;
  }
}
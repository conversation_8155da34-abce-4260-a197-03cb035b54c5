import { Component, h, State } from '@stencil/core';

@Component({
  tag: 'app-root',
  styleUrl: 'app-root.css',
  shadow: false // Changed to false to make debugging easier
})
export class AppRoot {
  @State() currentRoute: string = '/';
  @State() urlParams: URLSearchParams = new URLSearchParams();

  componentWillLoad() {
    console.log('App Root: componentWillLoad');
    // Set initial route based on window location
    this.handleRouteChange();
    
    // Listen for route changes from browser navigation
    window.addEventListener('popstate', () => this.handleRouteChange());
  }

  componentDidLoad() {
    console.log('App Root: componentDidLoad, current route:', this.currentRoute);
    // Listen for custom navigation events
    window.addEventListener('navchange', (event: CustomEvent) => {
      console.log('Navigation event received:', event.detail.path);
      this.currentRoute = event.detail.path.split('?')[0]; // Get the path without query params
      this.urlParams = new URLSearchParams(event.detail.path.includes('?') ? 
        event.detail.path.split('?')[1] : '');
      
      // Force a re-render by creating a new state object
      this.currentRoute = this.currentRoute + '';
    });
  }

  handleRouteChange() {
    const path = window.location.pathname;
    const search = window.location.search;
    console.log('Route changed to:', path, search);
    
    this.currentRoute = path;
    this.urlParams = new URLSearchParams(search);
    
    // Force a re-render by creating a new state object
    this.currentRoute = this.currentRoute + '';
  }
  
  // Simplified route rendering based on current path
  renderRoute() {
    console.log('Rendering route for:', this.currentRoute);
    
    if (this.currentRoute === '/' || this.currentRoute === '/select-residual-master') {
      return <residual-master-selection key={Date.now()}></residual-master-selection>;
    } else if (this.currentRoute.includes('/residual-master-settings')) {
      return <residual-settings key={Date.now()}></residual-settings>;
    } else if (this.currentRoute.includes('/edit-residual')) {
      return <residual-master-detail key={Date.now()}></residual-master-detail>;
    } else {
      return <residual-master-selection key={Date.now()}></residual-master-selection>;
    }
  }

  render() {
    return (
      <div class="app-container">
        <app-navbar></app-navbar>
        <main>
          {this.renderRoute()}
        </main>
      </div>
    );
  }
}
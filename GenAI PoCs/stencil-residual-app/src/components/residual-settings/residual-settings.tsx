import { Component, h, State } from '@stencil/core';
import { dataService } from '../../services/data.service';
import { ResidualMaster } from '../../models/residual-master.model';
import { formatDate } from '../../utils/date-helper';

@Component({
  tag: 'residual-settings',
  styleUrl: 'residual-settings.css',
  shadow: true,
})
export class ResidualSettings {
  @State() residualMaster: ResidualMaster;
  @State() error: string = '';
  @State() message: string = '';
  @State() loading: boolean = true;

  async componentWillLoad() {
    // Get the residual master ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const resMastID = urlParams.get('ResMastID');
    
    if (resMastID) {
      try {
        // Load the residual master data with await
        const residualMaster = await dataService.getResidualMasterById(parseInt(resMastID, 10));
        
        if (!residualMaster) {
          this.error = 'Residual master record not found';
        } else {
          this.residualMaster = residualMaster;
        }
      } catch (err) {
        this.error = 'Error loading residual master data';
        console.error(err);
      }
    } else {
      this.error = 'No residual master ID specified';
    }
    
    this.loading = false;
  }

  // Add navigation method
  navigate(path: string, event?: Event) {
    if (event) {
      event.preventDefault();
    }
    
    // Change the URL in the address bar
    window.history.pushState({}, '', path);
    
    // Dispatch a custom event with all the route data for app-root to handle
    const navEvent = new CustomEvent('navchange', {
      detail: { path },
      bubbles: true,
      composed: true
    });
    window.dispatchEvent(navEvent);
  }

  render() {
    if (this.loading) {
      return <div>Loading...</div>;
    }

    if (this.error) {
      return (
        <div>
          <message-display messages={this.error} messageType="error"></message-display>
          <a href="/select-residual-master">Back to Residual Master Selection</a>
        </div>
      );
    }

    return (
      <div class="residual-settings-container">
        <center>
          <h2>Residual Master Settings</h2>
          
          <message-display messages={this.error} messageType="error"></message-display>
          <message-display messages={this.message} messageType="message"></message-display>
          
          <table style={{ border: '0' }} cellSpacing="0" cellPadding="0">
            <tr>
              <td class="white-bg">&nbsp;</td>
            </tr>
            <tr>
              <td class="white-bg content-cell" style={{ verticalAlign: 'top' }}>
                {/* Display basic info about the selected residual master */}
                <div class="residual-info">
                  <h3>{this.residualMaster.record_title}</h3>
                  <p>Data Source: {this.residualMaster.data_source}</p>
                  <p>Model Year: {this.residualMaster.model_year}</p>
                  <p>New/Used: {this.residualMaster.new_used === 0 ? 'New' : 'Used'}</p>
                  <p>Start Date: {formatDate(this.residualMaster.startdate)}</p>
                  <p>Stop Date: {formatDate(this.residualMaster.stopdate)}</p>
                </div>
                
                <p>
                  <i>This is a placeholder for the Residual Master Settings page. In a full implementation, 
                  this would include forms for updating residual master settings.</i>
                </p>
                
                <div class="button-container">
                  <button class="submit-button" onClick={(e) => this.navigate(`/select-residual-master?DataSource=${this.residualMaster.data_source}&FiCoID=${this.residualMaster.fiCoID}`, e)}>
                    Back to Selection
                  </button>
                </div>
              </td>
            </tr>
          </table>
        </center>
      </div>
    );
  }
}
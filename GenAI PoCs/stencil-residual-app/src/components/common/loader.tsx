import { Component, h, Prop } from '@stencil/core';

@Component({
  tag: 'app-loader',
  styleUrl: 'loader.css',
  shadow: false
})
export class Loader {
  @Prop() text: string = 'Loading...';
  @Prop() fullScreen: boolean = false;

  render() {
    return (
      <div class={`loader-container ${this.fullScreen ? 'fullscreen' : ''}`}>
        <div class="loader-spinner"></div>
        {this.text && <div class="loader-text">{this.text}</div>}
      </div>
    );
  }
}
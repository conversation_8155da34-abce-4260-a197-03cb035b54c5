import { Component, Prop, h } from '@stencil/core';
import { Message } from '../../../models/residual-master.model';

@Component({
  tag: 'message-display',
  styleUrl: 'message-display.css',
  shadow: true,
})
export class MessageDisplay {
  @Prop() messages: Message[] | Message | string | string[];
  @Prop() messageType: 'error' | 'message' = 'message';
  
  render() {
    if (!this.messages) {
      return null;
    }
    
    // Convert to array of Message objects
    const messageArray = this.normalizeMessages();
    
    if (messageArray.length === 0) {
      return null;
    }
    
    return (
      <div>
        {messageArray.map((message) => (
          <div class={`message ${message.type}`}>
            {message.text}
          </div>
        ))}
      </div>
    );
  }
  
  private normalizeMessages(): Message[] {
    if (!this.messages) {
      return [];
    }
    
    // Handle string
    if (typeof this.messages === 'string') {
      return [{ text: this.messages, type: this.messageType as 'error' | 'message' }];
    }
    
    // Handle array of strings
    if (Array.isArray(this.messages) && this.messages.length > 0 && typeof this.messages[0] === 'string') {
      return (this.messages as string[]).map(text => ({ 
        text, 
        type: this.messageType as 'error' | 'message' 
      }));
    }
    
    // Handle single Message object
    if (!Array.isArray(this.messages) && (this.messages as Message).text) {
      return [this.messages as Message];
    }
    
    // Handle array of Message objects
    if (Array.isArray(this.messages)) {
      return this.messages as Message[];
    }
    
    return [];
  }
}
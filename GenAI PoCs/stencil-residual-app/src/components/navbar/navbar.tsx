import { Component, h } from '@stencil/core';

@Component({
  tag: 'app-navbar',
  styleUrl: 'navbar.css',
  shadow: true,
})
export class Navbar {
  navigate(path: string, event: MouseEvent) {
    event.preventDefault();
    window.history.pushState({}, '', path);
    // Dispatch a custom event that app-root can listen for
    const navEvent = new CustomEvent('navchange', {
      detail: { path },
      bubbles: true,
      composed: true
    });
    window.dispatchEvent(navEvent);
  }

  render() {
    return (
      <div class="navbar">
        <div class="navbar-container">
          <div class="navbar-left">
            <a href="/" class="navbar-brand" onClick={(e) => this.navigate('/', e)}>Residuals Entry</a>
          </div>
          <div class="navbar-right">
            <a href="/" class="navbar-link" onClick={(e) => this.navigate('/', e)}>Home</a>
            <a href="/select-residual-master" class="navbar-link" onClick={(e) => this.navigate('/select-residual-master', e)}>Select Residual Master</a>
          </div>
        </div>
      </div>
    );
  }
}
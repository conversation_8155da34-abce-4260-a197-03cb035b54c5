// Create mocks before importing any modules
const mockGetDataSources = jest.fn();
const mockGetFinanceCompaniesByDataSource = jest.fn();
const mockGetResidualMastersByDataSourceAndFinanceCompany = jest.fn();
const mockArchiveResidualMaster = jest.fn();
const mockDeleteResidualMaster = jest.fn();
const mockGetVehicleGroupsForResidualMaster = jest.fn();

// Mock the entire dataService module
jest.mock('../../services/data.service', () => ({
  dataService: {
    getDataSources: mockGetDataSources,
    getFinanceCompaniesByDataSource: mockGetFinanceCompaniesByDataSource,
    getResidualMastersByDataSourceAndFinanceCompany: mockGetResidualMastersByDataSourceAndFinanceCompany,
    archiveResidualMaster: mockArchiveResidualMaster,
    deleteResidualMaster: mockDeleteResidualMaster,
    getVehicleGroupsForResidualMaster: mockGetVehicleGroupsForResidualMaster
  }
}));

// Mock the formatDate function
jest.mock('../../utils/date-helper', () => ({
  formatDate: jest.fn().mockImplementation(date => date ? new Date(date).toLocaleDateString() : '')
}));

// Now import dependencies
import { newSpecPage } from '@stencil/core/testing';
import { ResidualMasterSelection } from './residual-master-selection';

describe('residual-master-selection', () => {
  // Mock data
  const mockDataSources = [
    { data_source: 'AIS', data_source_id: 1 },
    { data_source: 'DMS', data_source_id: 2 }
  ];

  const mockFinanceCompanies = [
    { fiCoID: 1, fiCoName: 'Toyota Financial', tblstatus_StatusID: 3 },
    { fiCoID: 2, fiCoName: 'Honda Financial', tblstatus_StatusID: 6 }
  ];

  const mockResidualMasters = [
    {
      res_mast_id: 1,
      record_title: 'Toyota Residuals 2023',
      model_year: 2023,
      residualDivisions: [{ division: { manufactName: 'Toyota', divName: 'Toyota' } }],
      new_used: 0,
      startdate: '2023-01-01T00:00:00.000Z',
      stopdate: '2023-12-31T00:00:00.000Z',
      tblstatus_StatusID: 3
    }
  ];

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup default mock implementations
    mockGetDataSources.mockResolvedValue(mockDataSources);
    mockGetFinanceCompaniesByDataSource.mockResolvedValue(mockFinanceCompanies);
    mockGetResidualMastersByDataSourceAndFinanceCompany.mockResolvedValue(mockResidualMasters);
    mockArchiveResidualMaster.mockResolvedValue(true);
    mockDeleteResidualMaster.mockResolvedValue(true);

    // Mock URLSearchParams for query parameters
    global.URLSearchParams = jest.fn().mockImplementation(() => ({
      get: jest.fn().mockImplementation(param => {
        switch (param) {
          case 'DataSource': return null;
          case 'FiCoID': return null;
          case 'Status': return null;
          case 'msg': return null;
          default: return null;
        }
      })
    }));

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/select-residual-master',
        search: '',
        href: '/select-residual-master',
        replace: jest.fn()
      },
      writable: true
    });

    // Properly mock window.confirm as a Jest mock function
    window.confirm = jest.fn().mockReturnValue(true);

    // Properly mock window.history.pushState as a Jest mock function
    window.history.pushState = jest.fn();

    // Mock window.dispatchEvent
    window.dispatchEvent = jest.fn();

    // Increase Jest timeout to avoid timeout errors
    jest.setTimeout(30000);
  });

  // Test 2: Renders data sources after loading
  it('renders data source selection after loading', async () => {
    // Render the component
    const page = await newSpecPage({
      components: [ResidualMasterSelection],
      html: `<residual-master-selection></residual-master-selection>`,
    });

    // Wait for component to initialize
    await page.waitForChanges();

    // Check data sources are loaded and rendered
    expect(page.rootInstance.dataSources).toEqual(mockDataSources);

    // Verify data source rendering
    const dataSourceElements = page.root.querySelectorAll('.residual-master-selection tr');
    // +1 for the header row
    expect(dataSourceElements.length).toBe(mockDataSources.length + 1);

    // Verify content of rendered elements
    expect(dataSourceElements[1].textContent).toContain('AIS');
    expect(dataSourceElements[2].textContent).toContain('DMS');
  });

  // Test 6: Handles archive action
  it('archives a residual master', async () => {
    // Render component with all necessary state
    const page = await newSpecPage({
      components: [ResidualMasterSelection],
      html: `<residual-master-selection></residual-master-selection>`,
    });

    page.rootInstance.dataSource = 'AIS';
    page.rootInstance.ficoId = 1;
    page.rootInstance.residualMasters = mockResidualMasters;
    await page.waitForChanges();

    // Mock Event
    const mockEvent = { preventDefault: jest.fn() };

    // Call archive method
    await page.rootInstance.handleArchiveResidualMaster(1, mockEvent as any);

    // Verify archive service was called
    expect(mockArchiveResidualMaster).toHaveBeenCalledWith(1);

    // Verify event was prevented
    expect(mockEvent.preventDefault).toHaveBeenCalled();

    // Verify location href change
    expect(window.location.href).toBe(`/select-residual-master?DataSource=AIS&FiCoID=1&msg=${encodeURIComponent('Residual master archived successfully')}`);
  });

  // Test 9: Handles error when archiving fails
  it('handles error when archiving fails', async () => {
    // Mock archive to fail
    mockArchiveResidualMaster.mockResolvedValueOnce(false);

    // Render component
    const page = await newSpecPage({
      components: [ResidualMasterSelection],
      html: `<residual-master-selection></residual-master-selection>`,
    });

    page.rootInstance.dataSource = 'AIS';
    page.rootInstance.ficoId = 1;

    // Mock Event
    const mockEvent = { preventDefault: jest.fn() };

    // Call archive method
    await page.rootInstance.handleArchiveResidualMaster(1, mockEvent as any);

    // Verify error state
    expect(page.rootInstance.error).toBe('Could not archive Residual Master');
  });

  // Test 14: Tests rendering of finance company selection
  it('renders finance company selection correctly', async () => {
    // Render component
    const page = await newSpecPage({
      components: [ResidualMasterSelection],
      html: `<residual-master-selection></residual-master-selection>`,
    });

    // Set data source but not ficoId
    page.rootInstance.dataSource = 'AIS';
    page.rootInstance.financeCompanies = mockFinanceCompanies;
    await page.waitForChanges();

    // Verify finance company selection is rendered
    const financeCompanyElements = page.root.querySelectorAll('.residual-master-selection tr');
    expect(financeCompanyElements.length).toBeGreaterThan(0);

    // Verify finance company content
    const financeCompanyElementsText = Array.from(financeCompanyElements).map(el => el.textContent);
    expect(financeCompanyElementsText.some(text => text.includes('Toyota Financial'))).toBe(true);
    expect(financeCompanyElementsText.some(text => text.includes('Honda Financial'))).toBe(true);
  });

  // Test 15: Tests rendering of residual master list
  it('renders residual master list correctly', async () => {
    // Render component
    const page = await newSpecPage({
      components: [ResidualMasterSelection],
      html: `<residual-master-selection></residual-master-selection>`,
    });

    // Set data source and ficoId
    page.rootInstance.dataSource = 'AIS';
    page.rootInstance.ficoId = 1;
    page.rootInstance.residualMasters = mockResidualMasters;
    await page.waitForChanges();

    // Verify residual master list is rendered
    const residualMasterRows = page.root.querySelectorAll('.residual-master-selection tr');
    expect(residualMasterRows.length).toBeGreaterThan(0);

    // Verify content
    const rowTexts = Array.from(residualMasterRows).map(el => el.textContent);
    expect(rowTexts.some(text => text.includes('Toyota Residuals 2023'))).toBe(true);
  });
});
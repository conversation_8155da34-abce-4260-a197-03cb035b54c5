import { Component, h, State } from '@stencil/core';
import { dataService } from '../../services/data.service';
import { ResidualMaster, FinanceCompany, DataSource } from '../../models/residual-master.model';
import { formatDate } from '../../utils/date-helper';

@Component({
  tag: 'residual-master-selection',
  styleUrl: 'residual-master-selection.css',
  shadow: false, // Disabled shadow DOM for debugging
})
export class ResidualMasterSelection {
  @State() dataSource: string = '';
  @State() ficoId: number = null;
  @State() status: string = '';
  @State() residualMasters: ResidualMaster[] = [];
  @State() financeCompanies: FinanceCompany[] = [];
  @State() dataSources: DataSource[] = [];
  @State() error: string = '';
  @State() message: string = '';
  @State() loading: boolean = true;

  async componentWillLoad() {
    try {
      this.loading = true;
      // Load data sources with await
      this.dataSources = await dataService.getDataSources();

      // Get query parameters from URL
      const urlParams = new URLSearchParams(window.location.search);
      const dataSourceParam = urlParams.get('DataSource');
      const ficoIdParam = urlParams.get('FiCoID');
      const statusParam = urlParams.get('Status');
      const msgParam = urlParams.get('msg');

      // Set state based on URL parameters
      if (dataSourceParam) {
        this.dataSource = dataSourceParam;
        this.financeCompanies = await dataService.getFinanceCompaniesByDataSource(this.dataSource);

        if (ficoIdParam) {
          this.ficoId = parseInt(ficoIdParam, 10);
          this.status = statusParam || '';
          this.residualMasters = await dataService.getResidualMastersByDataSourceAndFinanceCompany(
            this.dataSource, this.ficoId, this.status
          );
        }
      }

      // Set message if present in URL
      if (msgParam) {
        this.message = decodeURIComponent(msgParam);
      }
    } catch (err) {
      this.error = 'Error loading data';
      console.error(err);
    }
    finally {
      this.loading = false;
    }
  }

  // Add a navigation method to handle client-side routing

  async navigate(path: string, event?: Event) {
    if (event) {
      event.preventDefault();
    }

    // Change the URL in the address bar
    window.history.pushState({}, '', path);

    // Dispatch a custom event with all the route data for app-root to handle
    const navEvent = new CustomEvent('navchange', {
      detail: { path },
      bubbles: true,
      composed: true
    });
    window.dispatchEvent(navEvent);

    // For routes that require a component refresh, reload component data
    const currentPath = path.split('?')[0];
    if (currentPath === window.location.pathname) {
      try {
        this
        // Same route with different parameters - handle parameter changes
        const urlParams = new URLSearchParams(path.includes('?') ? path.split('?')[1] : '');

        // Update component state based on URL parameters
        const dataSourceParam = urlParams.get('DataSource');
        const ficoIdParam = urlParams.get('FiCoID');
        const statusParam = urlParams.get('Status');
        const msgParam = urlParams.get('msg');

        // Set state based on URL parameters
        if (dataSourceParam) {
          this.dataSource = dataSourceParam;
          this.financeCompanies = await dataService.getFinanceCompaniesByDataSource(this.dataSource);

          if (ficoIdParam) {
            this.ficoId = parseInt(ficoIdParam, 10);
            this.status = statusParam || '';
            this.residualMasters = await dataService.getResidualMastersByDataSourceAndFinanceCompany(
              this.dataSource, this.ficoId, this.status
            );
          }
        }

        // Set message if present in URL
        if (msgParam) {
          this.message = decodeURIComponent(msgParam);
        }
      } catch (err) {
        this.error = 'Error loading data';
        console.error(err);
      }
      finally {
        this.loading = false;
      }
    }
  }

  // Replace direct window.location.href assignments with our navigate method
  handleDataSourceSelection(dataSource: string) {
    this.navigate(`${window.location.pathname}?DataSource=${dataSource}`);
  }

  handleFinanceCompanySelection(ficoId: number) {
    this.navigate(`${window.location.pathname}?DataSource=${this.dataSource}&FiCoID=${ficoId}`);
  }

  handleStatusToggle() {
    const newStatus = this.status === 'Archived' ? '' : 'Archived';
    this.navigate(`${window.location.pathname}?DataSource=${this.dataSource}&FiCoID=${this.ficoId}&Status=${newStatus}`);
  }

  async handleArchiveResidualMaster(id: number, event: Event) {
    event.preventDefault();

    try {
      const success = await dataService.archiveResidualMaster(id);
      if (success) {
        // Reload the page with a success message
        window.location.href = `${window.location.pathname}?DataSource=${this.dataSource}&FiCoID=${this.ficoId}&msg=${encodeURIComponent('Residual master archived successfully')}`;
      } else {
        this.error = 'Could not archive Residual Master';
      }
    } catch (err) {
      this.error = 'Error archiving Residual Master';
    }
  }

  async handleDeleteResidualMaster(id: number, event: Event) {
    event.preventDefault();

    if (!confirm('Are you sure you want to delete this Residual Master?')) {
      return;
    }

    try {
      const success = await dataService.deleteResidualMaster(id);
      if (success) {
        // Reload the page with a success message
        window.location.href = `${window.location.pathname}?DataSource=${this.dataSource}&FiCoID=${this.ficoId}&msg=${encodeURIComponent('Residual master deleted successfully')}`;
      } else {
        this.error = 'Could not delete Residual Master';
      }
    } catch (err) {
      this.error = 'Error deleting Residual Master';
    }
  }

  renderDataSourceSelection() {
    return (
      <div class="blue-border">
        <table cellSpacing="0" cellPadding="5" class="residual-master-selection" style={{ width: '100%' }}>
          <tr>
            <th>Finance Company</th>
            <th>Action</th>
          </tr>

          {this.dataSources.map(source => (
            <tr>
              <td>{source.data_source}</td>
              <td>
                <button class="submit-button" onClick={() => this.handleDataSourceSelection(source.data_source)}>
                  View
                </button>
              </td>
            </tr>
          ))}
        </table>
      </div>
    );
  }

  renderFinanceCompanySelection() {
    return (
      <div>
        <center>
          <a href={`${window.location.pathname}?DataSource=${this.dataSource}&viewAll=true`} class="action-link">
            View All Residual Files
          </a>
        </center>
        <div class="blue-border">
          <table cellSpacing="0" cellPadding="0" class="residual-master-selection" style={{ width: '100%' }}>
            <tr>
              <th>Finance Company</th>
              <th>Status</th>
              <th>Action</th>
            </tr>

            {this.financeCompanies.map(company => (
              <tr>
                <td>{company.fiCoName}</td>
                <td style={{ width: '50px' }} class={company.tblstatus_StatusID === 6 ? 'status-pending' : 'status-active'}>
                  {company.tblstatus_StatusID === 6 ? 'Pending' : 'Active'}
                </td>
                <td>
                  <button class="submit-button" onClick={() => this.handleFinanceCompanySelection(company.fiCoID)}>
                    View
                  </button>
                </td>
              </tr>
            ))}
          </table>
        </div>
      </div>
    );
  }

  renderResidualMasterList() {
    return (
      <div>
        <center>
          <a href="#" class="action-link" onClick={() => this.handleStatusToggle()}>
            {this.status === 'Archived' ? 'View Active And Pending Residual Files' : 'View Archived Residual Files'}
          </a>
        </center>
        <br />
        <div class="blue-border">
          <table cellSpacing="0" cellPadding="0" class="residual-master-selection" style={{ width: '100%' }}>
            <tr>
              <th>Name</th>
              <th>Year</th>
              <th>Manufacturer</th>
              <th>Divisions</th>
              <th>New/Used</th>
              <th>Start Date</th>
              <th>Stop Date</th>
              <th>Status</th>
              <th>Action</th>
            </tr>

            {this.residualMasters.map(resMaster => {
              // Format divisions list
              const divisions = resMaster.residualDivisions.map(d => d.division.divName).join(', ');
              console.log(divisions);
              console.log(resMaster);
              return (
                <tr>
                  <td style={{ textAlign: 'center' }}><b>{resMaster.record_title}</b></td>
                  <td style={{ textAlign: 'center' }}>{resMaster.model_year}</td>
                  <td style={{ textAlign: 'center' }}>{resMaster.residualDivisions.length > 0 ? resMaster.residualDivisions[0].division.manufactName : 'Not specified'}</td>
                  <td style={{ textAlign: 'center' }}>{divisions || 'Not specified'}</td>
                  <td style={{ textAlign: 'center' }}>{resMaster.new_used === 0 ? 'New' : 'Used'}</td>
                  <td style={{ textAlign: 'center' }}>{formatDate(resMaster.startdate)}</td>
                  <td style={{ textAlign: 'center' }}>{formatDate(resMaster.stopdate)}</td>
                  <td style={{ width: '50px' }} class={
                    resMaster.tblstatus_StatusID === 6 ? 'status-pending' :
                      resMaster.tblstatus_StatusID === 3 ? 'status-active' :
                        'status-archived'
                  }>
                    {resMaster.tblstatus_StatusID === 6 ? 'Pending' :
                      resMaster.tblstatus_StatusID === 3 ? 'Active' : 'Archived'}
                  </td>
                  <td style={{ textAlign: 'center' }} class="action-buttons">
                    <button class="submit-button"
                      onClick={(e) => this.navigate(`/residual-master-settings?ResMastID=${resMaster.res_mast_id}`, e)}>
                      Settings
                    </button>

                    <button class="submit-button"
                      onClick={(e) => this.navigate(`/edit-residual?ResMastID=${resMaster.res_mast_id}`, e)}>
                      View
                    </button>

                    {this.status === 'Archived' ? (
                      <button class="submit-button"
                        onClick={(event) => this.handleDeleteResidualMaster(resMaster.res_mast_id, event)}>
                        Delete
                      </button>
                    ) : (
                      <button class="submit-button"
                        onClick={(event) => this.handleArchiveResidualMaster(resMaster.res_mast_id, event)}>
                        Archive
                      </button>
                    )}
                  </td>
                </tr>
              );
            })}
          </table>
        </div>
      </div>
    );
  }

  render() {
    if (this.loading == true) {
      return <app-loader text="Loading data..." fullScreen={true}></app-loader>;
    }
    return (
      <div class="residual-master-selection-container">
        <center>
          <h2>Select a Residual Master Record:</h2>

          <message-display messages={this.error} messageType="error"></message-display>
          <message-display messages={this.message} messageType="message"></message-display>

          <table style={{ border: '0' }} cellSpacing="0" cellPadding="0">
            <tr>
              <td class="white-bg">&nbsp;</td>
            </tr>
            <tr>
              <td class="white-bg content-cell" style={{ verticalAlign: 'top' }}>
                {/* Data Source Selection */}
                <div>
                  Data Source:
                  {!this.dataSource ? (
                    this.renderDataSourceSelection()
                  ) : (
                    <div>
                      <b>{this.dataSource}</b> -
                      <a href={window.location.pathname} class="action-link">Change</a>
                      <input type="hidden" name="DataSource" value={this.dataSource} />
                      <hr />
                    </div>
                  )}
                </div>

                {console.log('fcoid')}
                {console.log(this.ficoId)}
                {/* Finance Company Selection - Only show if dataSource is selected but ficoId is not */}
                {this.dataSource && !this.ficoId && this.renderFinanceCompanySelection()}

                {/* Finance Company Display - Show if both dataSource and ficoId are selected */}
                {this.dataSource && this.ficoId && (
                  <div>
                    Finance Company:
                    <input type="hidden" name="FiCoID" value={this.ficoId} />
                    {this.financeCompanies
                      .filter(company => company.fiCoID === this.ficoId)
                      .map(company => (
                        <div>
                          <b>{company.fiCoName}</b> -
                          <a href={`${window.location.pathname}?DataSource=${this.dataSource}`} class="action-link">Change</a>
                          <hr />
                        </div>
                      ))
                    }
                  </div>
                )}

                {/* Residual Master List - Show if both dataSource and ficoId are selected */}
                {console.log(this.dataSource)}
                {console.log(this.ficoId)}
                {console.log(this.renderResidualMasterList())}
                {this.dataSource && this.ficoId && this.renderResidualMasterList()}
              </td>
            </tr>
          </table>
        </center>
      </div>
    );
  }
}
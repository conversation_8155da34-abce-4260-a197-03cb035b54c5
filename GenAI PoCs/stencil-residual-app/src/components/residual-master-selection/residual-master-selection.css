.residual-master-selection-container {
  font-family: Arial, sans-serif;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.residual-master-selection-container h2 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.white-bg {
  background-color: #FFFFFF;
}

.content-cell {
  padding: 20px;
  text-align: left;
}

.blue-border {
  border: 1px solid blue;
  padding: 15px 0 0 0;
  margin-top: 15px;
}

.residual-master-selection {
  width: 100%;
  border-collapse: collapse;
}

.residual-master-selection th {
  background-color: #f2f2f2;
  padding: 8px;
  text-align: center;
  border-bottom: 1px solid #ddd;
}

.residual-master-selection td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.status-pending {
  background-color: pink;
}

.status-active {
  background-color: lightgreen;
}

.status-archived {
  background-color: lightgray;
}

.submit-button {
  background-color: #0066cc;
  color: white;
  border: none;
  padding: 5px 10px;
  margin: 0 10px;
  cursor: pointer;
  border-radius: 3px;
}

.submit-button:hover {
  background-color: #0052a3;
}

.action-link {
  color: #0000ff;
  font-size: 12px;
  font-weight: bold;
  font-style: italic;
  text-decoration: none;
  margin-left: 5px;
}

.action-link:hover {
  text-decoration: underline;
}

.action-buttons {
  white-space: nowrap;
}
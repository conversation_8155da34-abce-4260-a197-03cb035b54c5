import { ResidualMaster, FinanceCompany, DataSource } from '../models/residual-master.model';

class DataService {
  private apiUrl = 'https://localhost:7213/api'; // Default ASP.NET Core port with HTTPS

  // Fetch all residual masters
  async getResidualMasters(): Promise<ResidualMaster[]> {
    try {
      const response = await fetch(`${this.apiUrl}/ResidualMasters`);
      if (!response.ok) {
        throw new Error('Failed to fetch residual masters');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching residual masters:', error);
      return [];
    }
  }

  // Fetch all finance companies
  async getFinanceCompanies(): Promise<FinanceCompany[]> {
    try {
      const response = await fetch(`${this.apiUrl}/FinanceCompanies`);
      if (!response.ok) {
        throw new Error('Failed to fetch finance companies');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching finance companies:', error);
      return [];
    }
  }

  // Fetch all data sources
  async getDataSources(): Promise<DataSource[]> {
    try {
      const response = await fetch(`${this.apiUrl}/DataSources`);
      if (!response.ok) {
        throw new Error('Failed to fetch data sources');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching data sources:', error);
      return [];
    }
  }

  // Fetch residual masters by data source
  async getResidualMastersByDataSource(dataSource: string): Promise<ResidualMaster[]> {
    try {
      const response = await fetch(`${this.apiUrl}/ResidualMasters/dataSource/${dataSource}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch residual masters for data source: ${dataSource}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching residual masters for data source ${dataSource}:`, error);
      return [];
    }
  }

  // Fetch residual masters by finance company
  async getResidualMastersByFinanceCompany(ficoId: number): Promise<ResidualMaster[]> {
    try {
      const response = await fetch(`${this.apiUrl}/ResidualMasters/financeCompany/${ficoId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch residual masters for finance company ID: ${ficoId}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching residual masters for finance company ID ${ficoId}:`, error);
      return [];
    }
  }

  // Fetch residual masters by data source and finance company
  async getResidualMastersByDataSourceAndFinanceCompany(
    dataSource: string, 
    ficoId: number, 
    status?: string
  ): Promise<ResidualMaster[]> {
    try {
      const response = await fetch(`${this.apiUrl}/ResidualMasters/dataSource/${dataSource}/financeCompany/${ficoId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch residual masters for data source: ${dataSource} and finance company ID: ${ficoId}`);
      }
      let result = await response.json();
      
      // Filter by status if provided
      if (status === 'Archived') {
        return result.filter(rm => rm.tblstatus_StatusID === 5);
      } else if (status === '') {
        // Show active and pending (non-archived)
        return result.filter(rm => rm.tblstatus_StatusID !== 5);
      }
      
      return result;
    } catch (error) {
      console.error(`Error fetching residual masters for data source ${dataSource} and finance company ID ${ficoId}:`, error);
      return [];
    }
  }

  // Fetch finance companies by data source
  async getFinanceCompaniesByDataSource(dataSource: string): Promise<FinanceCompany[]> {
    try {
      const response = await fetch(`${this.apiUrl}/FinanceCompanies/dataSource/${dataSource}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch finance companies for data source: ${dataSource}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching finance companies for data source ${dataSource}:`, error);
      return [];
    }
  }

  // Fetch a specific residual master by ID
  async getResidualMasterById(id: number): Promise<ResidualMaster | undefined> {
    try {
      const response = await fetch(`${this.apiUrl}/ResidualMasters/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          return undefined;
        }
        throw new Error(`Failed to fetch residual master with ID: ${id}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching residual master with ID ${id}:`, error);
      return undefined;
    }
  }

  // Delete a residual master
  async deleteResidualMaster(id: number): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/ResidualMasters/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      return response.ok;
    } catch (error) {
      console.error(`Error deleting residual master with ID ${id}:`, error);
      return false;
    }
  }

  // Archive a residual master
  async archiveResidualMaster(id: number): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/ResidualMasters/${id}/archive`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      return response.ok;
    } catch (error) {
      console.error(`Error archiving residual master with ID ${id}:`, error);
      return false;
    }
  }

  // Save residual vehicles and adjustments
  async saveResidualVehicles(residualMasterId: number, vehicles: any[]): Promise<boolean> {
    try {
      console.log(residualMasterId);
      console.log(vehicles);
      const response = await fetch(`${this.apiUrl}/ResidualMasters/${residualMasterId}/vehicles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(vehicles)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save residual vehicles: ${response.statusText}`);
      }
      
      return true;
    } catch (error) {
      console.error(`Error saving residual vehicles for master ID ${residualMasterId}:`, error);
      return false;
    }
  }

  // Fetch vehicle groups
  async getVehicleGroups(): Promise<any[]> {
    try {
      const response = await fetch(`${this.apiUrl}/VehicleGroups`);
      if (!response.ok) {
        throw new Error('Failed to fetch vehicle groups');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching vehicle groups:', error);
      return [];
    }
  }

  // Fetch vehicle groups for a specific residual master
  async getVehicleGroupsForResidualMaster(residualMasterId: number): Promise<any[]> {
    try {
      const response = await fetch(`${this.apiUrl}/VehicleGroups/forResidualMaster/${residualMasterId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch vehicle groups for residual master ID: ${residualMasterId}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error fetching vehicle groups for residual master ID ${residualMasterId}:`, error);
      return [];
    }
  }
}

export const dataService = new DataService();
/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
import { Message } from "./models/residual-master.model";
export namespace Components {
    interface AppLoader {
        "fullScreen": boolean;
        "text": string;
    }
    interface AppNavbar {
    }
    interface AppRoot {
    }
    interface MessageDisplay {
        "messageType": 'error' | 'message';
        "messages": Message[] | Message | string | string[];
    }
    interface ResidualMasterDetail {
    }
    interface ResidualMasterSelection {
    }
    interface ResidualSettings {
    }
}
declare global {
    interface HTMLAppLoaderElement extends Components.AppLoader, HTMLStencilElement {
    }
    var HTMLAppLoaderElement: {
        prototype: HTMLAppLoaderElement;
        new (): HTMLAppLoaderElement;
    };
    interface HTMLAppNavbarElement extends Components.AppNavbar, HTMLStencilElement {
    }
    var HTMLAppNavbarElement: {
        prototype: HTMLAppNavbarElement;
        new (): HTMLAppNavbarElement;
    };
    interface HTMLAppRootElement extends Components.AppRoot, HTMLStencilElement {
    }
    var HTMLAppRootElement: {
        prototype: HTMLAppRootElement;
        new (): HTMLAppRootElement;
    };
    interface HTMLMessageDisplayElement extends Components.MessageDisplay, HTMLStencilElement {
    }
    var HTMLMessageDisplayElement: {
        prototype: HTMLMessageDisplayElement;
        new (): HTMLMessageDisplayElement;
    };
    interface HTMLResidualMasterDetailElement extends Components.ResidualMasterDetail, HTMLStencilElement {
    }
    var HTMLResidualMasterDetailElement: {
        prototype: HTMLResidualMasterDetailElement;
        new (): HTMLResidualMasterDetailElement;
    };
    interface HTMLResidualMasterSelectionElement extends Components.ResidualMasterSelection, HTMLStencilElement {
    }
    var HTMLResidualMasterSelectionElement: {
        prototype: HTMLResidualMasterSelectionElement;
        new (): HTMLResidualMasterSelectionElement;
    };
    interface HTMLResidualSettingsElement extends Components.ResidualSettings, HTMLStencilElement {
    }
    var HTMLResidualSettingsElement: {
        prototype: HTMLResidualSettingsElement;
        new (): HTMLResidualSettingsElement;
    };
    interface HTMLElementTagNameMap {
        "app-loader": HTMLAppLoaderElement;
        "app-navbar": HTMLAppNavbarElement;
        "app-root": HTMLAppRootElement;
        "message-display": HTMLMessageDisplayElement;
        "residual-master-detail": HTMLResidualMasterDetailElement;
        "residual-master-selection": HTMLResidualMasterSelectionElement;
        "residual-settings": HTMLResidualSettingsElement;
    }
}
declare namespace LocalJSX {
    interface AppLoader {
        "fullScreen"?: boolean;
        "text"?: string;
    }
    interface AppNavbar {
    }
    interface AppRoot {
    }
    interface MessageDisplay {
        "messageType"?: 'error' | 'message';
        "messages"?: Message[] | Message | string | string[];
    }
    interface ResidualMasterDetail {
    }
    interface ResidualMasterSelection {
    }
    interface ResidualSettings {
    }
    interface IntrinsicElements {
        "app-loader": AppLoader;
        "app-navbar": AppNavbar;
        "app-root": AppRoot;
        "message-display": MessageDisplay;
        "residual-master-detail": ResidualMasterDetail;
        "residual-master-selection": ResidualMasterSelection;
        "residual-settings": ResidualSettings;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "app-loader": LocalJSX.AppLoader & JSXBase.HTMLAttributes<HTMLAppLoaderElement>;
            "app-navbar": LocalJSX.AppNavbar & JSXBase.HTMLAttributes<HTMLAppNavbarElement>;
            "app-root": LocalJSX.AppRoot & JSXBase.HTMLAttributes<HTMLAppRootElement>;
            "message-display": LocalJSX.MessageDisplay & JSXBase.HTMLAttributes<HTMLMessageDisplayElement>;
            "residual-master-detail": LocalJSX.ResidualMasterDetail & JSXBase.HTMLAttributes<HTMLResidualMasterDetailElement>;
            "residual-master-selection": LocalJSX.ResidualMasterSelection & JSXBase.HTMLAttributes<HTMLResidualMasterSelectionElement>;
            "residual-settings": LocalJSX.ResidualSettings & JSXBase.HTMLAttributes<HTMLResidualSettingsElement>;
        }
    }
}

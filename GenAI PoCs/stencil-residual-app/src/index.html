<!DOCTYPE html>
<html dir="ltr" lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0">
  <title>Residuals Entry Application</title>
  
  <!-- Add this for debugging in the console -->
  <script>
    window.addEventListener('DOMContentLoaded', () => {
      console.log('DOM fully loaded');
    });
  </script>
  
  <link rel="stylesheet" href="/build/residual-app.css">
  <script type="module" src="/build/residual-app.esm.js"></script>
  <script nomodule src="/build/residual-app.js"></script>
</head>
<body>
  <app-root></app-root>
  
  <!-- Add this to see if we can render a component directly -->
  <script>
    setTimeout(() => {
      console.log('Checking for custom elements:');
      console.log('app-root defined:', !!customElements.get('app-root'));
      console.log('residual-master-selection defined:', !!customElements.get('residual-master-selection'));
      console.log('app-navbar defined:', !!customElements.get('app-navbar'));
    }, 2000);
  </script>
</body>
</html>
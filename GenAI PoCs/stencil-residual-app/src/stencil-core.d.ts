// This is a minimal type declaration file for @stencil/core
// It declares just enough types to make TypeScript happy with the imports

declare module '@stencil/core' {
  export const h: any;
  export interface ComponentInterface {}
  
  export function Component(opts: any): ClassDecorator;
  export function Prop(opts?: any): PropertyDecorator;
  export function State(opts?: any): PropertyDecorator;
  export function Method(opts?: any): MethodDecorator;
  export function Element(opts?: any): PropertyDecorator;
  export function Event(opts?: any): PropertyDecorator;
  export function Listen(eventName: string, opts?: any): MethodDecorator;
  export function Watch(propName: string): MethodDecorator;
}
export interface ResidualMaster {
  res_mast_id: number;
  record_title: string;
  model_year: string;
  new_used: number; // 0 = New, 1 = Used
  startdate: string;
  stopdate: string;
  tblstatus_StatusID: number; // 3 = Active, 5 = Archived, 6 = Pending
  data_source: string;
  filename?: string;
  fiCoID?: number;
  fiCoName?: string;
  residualMasterTermTemplates: ResidualMasterTermTemplate[];
  vehicle_group_source?: string;
  residualDivisions: {division:Division}[];
  residualVehicleGroupData: { factory_invoice: number, msrp: number,residualValues: ResidualValue[], residualVehicleGroup:
    ResidualVehicleGroup, residualDataAdjustments: ResidualAdjustment[]  }[]
}
export interface ResidualValue{
  id: number;
  term: number;
  mileage: number;
  value: string;
  isEditing?: boolean;
  pgmTermID: number;
  value_type: number;
}

export interface Division {
  divisionID: number;
  divName: string;
  manufactName: string;
}

export interface ResidualAdjustment {
  residual_data_adjustment_id : number;
  adjustment_value: number;
  msrp: number;
  mrm?: string;
  ResidualAdjustCode: number;
  pgmTermID: number;
  ResidualVehicleGroupDataID: number;
  terms: { [key: number]: number };
}

export interface ResidualMasterTermTemplate{
  id: number;
  pgmTermID: number;
  programTerm: ProgramTerm;
  residualmasterID: number;
}

export interface ProgramTerm{
  pgmTermID: number;
  pgmTermMax: number;
  pgmTermMin: number;
}
export interface ResidualVehicleGroup{
  division: Division,
  vehicle_group_name: string,
  residual_vehicle_group_id: number,
  vehicle_group_year: string
}

export interface FinanceCompany {
  fiCoID: number;
  fiCoName: string;
  tblstatus_StatusID: number;
}

export interface DataSource {
  data_source: string;
}

export interface Message {
  text: string;
  type: 'error' | 'message';
}
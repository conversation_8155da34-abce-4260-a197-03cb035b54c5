import '@testing-library/jest-dom';

// Properly setup jasmine globals for Stencil compatibility
global.jasmine = {
  createSpy: () => jest.fn(),
  createSpyObj: (baseName, methodNames) => {
    const obj = {};
    for (const key of methodNames) {
      obj[key] = jest.fn();
    }
    return obj;
  }
};

// Don't mock Stencil core directly, as it's needed for the tests
// Instead, we'll use the testing version from the preset

// Setup proper window mocks
Object.defineProperty(window, 'URL', {
  value: {
    createObjectURL: jest.fn(),
    revokeObjectURL: jest.fn()
  }
});

// Mock CustomEvent constructor
if (typeof window.CustomEvent !== 'function') {
  class CustomEvent extends Event {
    detail: any;
    constructor(event, params) {
      params = params || { bubbles: false, cancelable: false, detail: null };
      super(event, params);
      this.detail = params.detail;
    }
  }
  
  window.CustomEvent = CustomEvent as any;
}
import { Config } from '@stencil/core';
import { sass } from '@stencil/sass';

export const config: Config = {
  namespace: 'residual-app',
  globalStyle: 'src/global/app.css',
  outputTargets: [
    {
      type: 'www',
      serviceWorker: null, // disable service workers
      baseUrl: '/',
      // copy: [
      //   { src: 'assets', warn: true }
      // ]
    },
    {
      type: 'dist',
      esmLoaderPath: '../loader'
    }
  ],
  plugins: [
    sass()
  ],
  devServer: {
    reloadStrategy: 'pageReload',
    port: 3333,
    openBrowser: true
  },
  buildEs5: false,
  extras: {
    cssVarsShim: false,
    dynamicImportShim: false,
    safari10: false,
    shadowDomShim: false
  },
  testing: {
    browserHeadless: true,
    collectCoverage: true,
    coverageDirectory: 'coverage',
    coverageReporters: ['lcov', 'text']
  }
};
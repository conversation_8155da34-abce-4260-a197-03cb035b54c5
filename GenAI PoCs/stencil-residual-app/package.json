{"name": "stencil-residual-app", "version": "0.1.0", "description": "StencilJS Residuals Entry Application", "main": "dist/index.cjs.js", "module": "dist/index.js", "es2015": "dist/esm/index.js", "es2017": "dist/esm/index.js", "types": "dist/types/index.d.ts", "collection": "dist/collection/collection-manifest.json", "collection:main": "dist/collection/index.js", "unpkg": "dist/residual-app/residual-app.esm.js", "scripts": {"build": "stencil build --docs", "start": "stencil build --dev --watch --serve", "generate": "stencil generate", "test": "stencil test --spec", "test.watch": "stencil test --spec --watchAll", "test.end-to-end": "stencil test --e2e", "test:coverage": "stencil test --spec --coverage", "coverage": "stencil test --spec --coverage --coverageReporters=lcov --coverageReporters=text"}, "dependencies": {"@stencil/core": "^2.22.3", "@stencil/router": "^1.0.1"}, "devDependencies": {"@stencil/sass": "^2.0.3", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^27.5.2", "@types/node": "^18.11.18", "jest": "^27.5.1", "jest-cli": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-jasmine2": "^27.5.1", "puppeteer": "^19.5.2", "ts-jest": "^27.1.5", "ts-node": "^10.9.2"}, "license": "MIT"}
﻿using System.Data;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using VehicleExportWorkerService.Models;

namespace VehicleExportWorkerService.Data
{
    public class AisDataDao
    {
        private readonly IDbConnection _dbConnection;
        private readonly ILogger<AisDataDao> _logger;

        public AisDataDao(IDbConnection dbConnection, ILogger<AisDataDao> logger)
        {
            _dbConnection = dbConnection;
            _logger = logger;
        }

        public async Task TestConnectionAsync()
        {
            try
            {
                _logger.LogInformation("Testing database connection...");
                var result = await _dbConnection.QueryFirstOrDefaultAsync<string>("SELECT 1");
                _logger.LogInformation("Database connection test successful. Result: {Result}", result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database connection test failed.");
                throw;
            }
        }

        public async Task<List<ActiveDescVehicle>> GetAllActiveDescVehiclesAsync()
        {
            const string query = @"
                SELECT
                    v.DescVehicleID,
                    (SELECT DivName
                     FROM tblManufactDivisions
                     WHERE DivisionID = v.tblManufactDivisions_DivisionID) AS Division,
                    v.dvYear AS Year,
                    m.VehElementDescript AS Model,
                    v.ManufactModelCode,
                    v.ACode,
                    t.VehElementDescript AS Trim,
                    p.VehElementDescript AS Package,
                    b.VehElementDescript AS Body,
                    d.VehElementDescript AS Doors,
                    dt.VehElementDescript AS DriveType,
                    bl.VehElementDescript AS BlockType,
                    c.VehElementDescript AS Cylinders,
                    a.VehElementDescript AS Aspiration,
                    ind.VehElementDescript AS Induction,
                    tr.VehElementDescript AS Trans,
                    trs.VehElementDescript AS TranSpds,
                    bedl.VehElementDescript AS BedLength,
                    wb.VehElementDescript AS WheelBase,
                    o.VehElementDescript AS OptionGroup,
                    es.VehElementDescript AS EngSize,
                    f.VehElementDescript AS Fuel,
                    mec.VehElementDescript AS MfrEngCode,
                    mtc.VehElementDescript AS MfrTransCode
                FROM tblDescribedVehicles v
                JOIN tblVehicleElements m ON m.VehElementId = v.ModelEID
                JOIN tblVehicleElements t ON t.VehElementId = v.TrimEID
                JOIN tblVehicleElements p ON p.VehElementId = v.PackageEID
                JOIN tblVehicleElements d ON d.VehElementId = v.DoorsEID
                JOIN tblVehicleElements b ON b.VehElementId = v.BodyEID
                JOIN tblVehicleElements dt ON dt.VehElementId = v.DriveTypeEID
                JOIN tblVehicleElements es ON es.VehElementId = v.EngSizeEID
                JOIN tblVehicleElements f ON f.VehElementId = v.FuelEID
                JOIN tblVehicleElements bl ON bl.VehElementId = v.BlockTypeEID
                JOIN tblVehicleElements c ON c.VehElementId = v.CylindersEID
                JOIN tblVehicleElements a ON a.VehElementId = v.AspirationEID
                JOIN tblVehicleElements ind ON ind.VehElementId = v.InductionEID
                JOIN tblVehicleElements tr ON tr.VehElementId = v.TransEID
                JOIN tblVehicleElements trs ON trs.VehElementId = v.TranSpdsEID
                JOIN tblVehicleElements o ON o.VehElementId = v.OptionGroupEID
                JOIN tblVehicleElements mec ON mec.VehElementId = v.MfrEngCodeEID
                JOIN tblVehicleElements mtc ON mtc.VehElementId = v.MfrTransCodeEID
                JOIN tblVehicleElements bedl ON bedl.VehElementId = v.BedLengthEID
                JOIN tblVehicleElements wb ON wb.VehElementId = v.WheelBaseEID
                WHERE v.tblStatus_StatusID = '3'
                AND v.tblCountries_CountryID = '189'
                AND v.dvYear > @Year";

            var result = await _dbConnection.QueryAsync<ActiveDescVehicle>(query, new { Year = 2025 });
            return result.AsList();
        }
    }
}
﻿using System.Data;
using MySql.Data.MySqlClient;
using Microsoft.Extensions.Configuration;

namespace VehicleExportWorkerService.Data
{
    public class DatabaseContext
    {
        private readonly IConfiguration _configuration;

        public DatabaseContext(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public IDbConnection CreateConnection()
        {
            var connectionString = _configuration.GetSection("AppSettings:ConnectionStrings:DefaultConnection").Value;

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Database connection string is not configured.");
            }

            Console.WriteLine($"Using connection string: {connectionString}"); // Debugging log
            return new MySqlConnection(connectionString);
        }
    }
}
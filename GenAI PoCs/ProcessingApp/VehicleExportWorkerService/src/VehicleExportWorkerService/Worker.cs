using Microsoft.Extensions.Options;
using VehicleExportWorkerService.Models;
using VehicleExportWorkerService.Services;

namespace VehicleExportWorkerService;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IOptions<AppSettings> _settings;
    private readonly ActiveDescVehExport _activeDescVehExport;
    private readonly EmailService _emailService;

    public Worker(
        ILogger<Worker> logger,
        IOptions<AppSettings> settings,
        ActiveDescVehExport activeDescVehExport,
        EmailService emailService)
    {
        _logger = logger;
        _settings = settings;
        _activeDescVehExport = activeDescVehExport;
        _emailService = emailService;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Worker running at: {time}", DateTimeOffset.Now);

        // Test the database connection
        await _activeDescVehExport.TestDatabaseConnectionAsync();

        _activeDescVehExport.StartTimer();

        var activeDescVehFileName = await _activeDescVehExport.CreateActiveDescVehFileAsync(_settings.Value.ActiveDescVehSaveDirectory);

        if (string.IsNullOrEmpty(activeDescVehFileName))
        {
            _logger.LogInformation("No New Active Vehicle File was created");
        }
        else
        {
            if (await _emailService.SendEmailAsync(activeDescVehFileName))
            {
                _logger.LogInformation("Active Described Vehicle File Emailed");
            }
            else
            {
                _logger.LogInformation("No Email Sent");
            }
        }

        var finish = _activeDescVehExport.GetCurrentTime();
        var totalTime = Math.Round((finish - _activeDescVehExport.LastTimer).TotalSeconds, 4);
        _logger.LogInformation("Page generated in {totalTime} seconds.", totalTime);
        _logger.LogInformation("Active Described Vehicle Export Complete.");
        _logger.LogInformation("Final memory usage: {memoryUsage}", GC.GetTotalMemory(false));
    }
}
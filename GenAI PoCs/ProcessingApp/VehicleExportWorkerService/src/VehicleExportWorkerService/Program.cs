using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using VehicleExportWorkerService.Models;
using VehicleExportWorkerService.Services;
using VehicleExportWorkerService.Data;
using System.Data;

namespace VehicleExportWorkerService
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureServices((hostContext, services) =>
                {
                    var configuration = hostContext.Configuration.GetSection("AppSettings").Get<AppSettings>();

                    // Ensure all required properties are initialized
                    if (configuration == null)
                    {
                        throw new InvalidOperationException("AppSettings configuration is missing.");
                    }

                    services.Configure<AppSettings>(hostContext.Configuration.GetSection("AppSettings"));
                    services.AddSingleton<IDbConnection>(sp => new DatabaseContext(hostContext.Configuration).CreateConnection());
                    services.AddSingleton<AisDataDao>();
                    services.AddSingleton<ActiveDescVehExport>();
                    services.AddSingleton<EmailService>();
                    services.AddHostedService<Worker>();
                });
    }
}
using Amazon.Lambda.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Data;
using System.Threading.Tasks;
using VehicleExportWorkerService.Data;
using VehicleExportWorkerService.Models;
using VehicleExportWorkerService.Services;

// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]

namespace VehicleExportWorkerService;

public class Function
{
    protected IServiceProvider _serviceProvider;
    private IOptions<AppSettings> _appSettings;

    public Function()
    {
        // Initialize the service provider here if needed
        var hostBuilder = new HostBuilder()
            .ConfigureAppConfiguration(ConfigureApp)
            .ConfigureServices(ConfigureServices);
        var host = hostBuilder.Build();
        _serviceProvider = host.Services;
    }

    /// <summary>
    /// A simple function that takes a string and does a ToUpper
    /// </summary>
    /// <param name="input">The event for the Lambda function handler to process.</param>
    /// <param name="context">The ILambdaContext that provides methods for logging and describing the Lambda environment.</param>
    /// <returns></returns>
    public async Task FunctionHandler(string input, ILambdaContext context)
    {
        var scope = _serviceProvider.CreateScope();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Worker>>();
        var vehicleDescribedService = scope.ServiceProvider.GetRequiredService<ActiveDescVehExport>();
        var emailService = scope.ServiceProvider.GetRequiredService<EmailService>();
        _appSettings = scope.ServiceProvider.GetRequiredService<IOptions<AppSettings>>();
        var worker = new Worker(logger, _appSettings, vehicleDescribedService, emailService);
        await worker.ExecuteAsync(new CancellationToken());
       
    }

    private static void ConfigureApp(HostBuilderContext hostContext, IConfigurationBuilder configuration)
    {
        var appEnvironment = hostContext.HostingEnvironment.EnvironmentName;

        // Default to dev when running locally
        var environment = appEnvironment == "local" ? "dev" : appEnvironment;

        configuration
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json")
            .AddJsonFile($"appsettings.{environment}.json", false);
    }

    private static void ConfigureServices(HostBuilderContext hostContext, IServiceCollection services)
    {
        // Bind the AppSettings section to the AppSettings class
        services.Configure<AppSettings>(hostContext.Configuration.GetSection("AppSettings"));

        // Register AppSettings as a singleton for dependency injection
        services.AddSingleton(sp => sp.GetRequiredService<IOptions<AppSettings>>().Value);
        services.AddSingleton<HttpClient>();
        services.AddSingleton<IDbConnection>(sp => new DatabaseContext(hostContext.Configuration).CreateConnection());
        services.AddSingleton<AisDataDao>();
        services.AddSingleton<ActiveDescVehExport>();
        services.AddSingleton<EmailService>();
        services.AddSingleton<Worker>();
    }
}

{"Information": ["This file provides default values for the deployment wizard inside Visual Studio and the AWS Lambda commands added to the .NET Core CLI.", "To learn more about the Lambda commands with the .NET Core CLI execute the following command at the command line in the project root directory.", "dotnet lambda help", "All the command line options for the Lambda command can be specified in this file."], "profile": "ais-non-prod", "region": "us-east-1", "configuration": "Release", "function-architecture": "x86_64", "function-runtime": "dotnet8", "function-memory-size": 1024, "function-timeout": 60, "function-handler": "DescribedVehicleExportWorker::VehicleExportWorkerService.Function::FunctionHandler", "framework": "net8.0", "function-name": "DescribedVehicleExtraction", "function-description": "", "package-type": "Zip", "function-role": "arn:aws:iam::131579720124:role/acct-managed/Acct-StellantisExcelCompare-testing01-lambda", "function-subnets": "subnet-0562c406c7baac10c,subnet-0d67a5764d9811625", "function-security-groups": "sg-02a540051ce8c2814", "tracing-mode": "PassThrough", "environment-variables": "", "image-tag": ""}
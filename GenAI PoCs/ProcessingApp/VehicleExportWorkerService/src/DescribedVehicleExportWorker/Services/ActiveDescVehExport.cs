﻿using Microsoft.Extensions.Logging;
using System.IO.Compression;
using VehicleExportWorkerService.Data;

namespace VehicleExportWorkerService.Services;

public class ActiveDescVehExport
{
    private readonly ILogger<ActiveDescVehExport> _logger;
    private readonly AisDataDao _aisDataDao;
    public DateTime LastTimer { get; private set; }

    public ActiveDescVehExport(ILogger<ActiveDescVehExport> logger, AisDataDao aisDataDao)
    {
        _logger = logger;
        _aisDataDao = aisDataDao;
    }

    public void StartTimer()
    {
        LastTimer = DateTime.Now;
    }

    public DateTime GetCurrentTime()
    {
        return DateTime.Now;
    }

    public async Task TestDatabaseConnectionAsync()
    {
        await _aisDataDao.TestConnectionAsync();
    }

    public async Task<string> CreateActiveDescVehFileAsync(string saveDirectory)
    {
        var actDescVehs = await _aisDataDao.GetAllActiveDescVehiclesAsync();
        if (actDescVehs.Count == 0)
        {
            _logger.LogInformation("No active described vehicles found.");
            return null;
        }

        var csvFileName = Path.Combine(saveDirectory, $"ActiveDescribedVehicles_{DateTime.Now:yyyy-MM-dd}.csv");
        await using (var writer = new StreamWriter(csvFileName))
        {
            // No headers, matching PHP implementation
            // Write each vehicle's data
            foreach (var vehicle in actDescVehs)
            {
                var line = string.Join(",",
                    vehicle.DescVehicleID,
                    vehicle.Division,
                    vehicle.Year,
                    vehicle.Model,
                    vehicle.ManufactModelCode,
                    vehicle.Trim,
                    vehicle.Package,
                    vehicle.Body,
                    vehicle.Doors,
                    vehicle.DriveType,
                    vehicle.BlockType,
                    vehicle.Cylinders,
                    vehicle.Aspiration,
                    vehicle.Induction,
                    vehicle.Trans,
                    vehicle.TranSpds,
                    vehicle.BedLength,
                    vehicle.WheelBase,
                    vehicle.OptionGroup,
                    vehicle.EngSize,
                    vehicle.Fuel,
                    vehicle.MfrEngCode,
                    vehicle.MfrTransCode,
                    vehicle.Style_Name
                );
                await writer.WriteLineAsync(line);
            }
        }

        var zipFileName = Path.Combine(saveDirectory, $"ActiveDescribedVehicles_{DateTime.Now:yyyy-MM-dd}.zip");

        // Check if the zip file already exists and delete it to avoid errors
        if (File.Exists(zipFileName))
        {
            _logger.LogInformation($"File {Path.GetFileName(zipFileName)} already exists. Deleting existing file.");
            File.Delete(zipFileName);
        }

        using (var zip = ZipFile.Open(zipFileName, ZipArchiveMode.Create))
        {
            zip.CreateEntryFromFile(csvFileName, Path.GetFileName(csvFileName));
        }

        File.Delete(csvFileName);
        _logger.LogInformation($"{Path.GetFileName(zipFileName)} created.");

        return zipFileName;
    }

}
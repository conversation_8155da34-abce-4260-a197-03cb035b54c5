﻿using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using VehicleExportWorkerService.Models;

namespace VehicleExportWorkerService.Services;

public class EmailService
{
    private readonly IOptions<AppSettings> _settings;
    private readonly ILogger<EmailService> _logger;

    public EmailService(IOptions<AppSettings> settings, ILogger<EmailService> logger)
    {
        _settings = settings;
        _logger = logger;
    }

    public async Task<bool> SendEmailAsync(string fileName)
    {
        var emailSettings = _settings.Value.EmailSettings;
        using var smtpClient = new SmtpClient(emailSettings.SmtpServer)
        {
            Port = emailSettings.SmtpPort,
            Credentials = new NetworkCredential(emailSettings.SmtpUser, emailSettings.SmtpPassword),
            EnableSsl = true
        };

        // Dynamic subject with current date - matching PHP implementation
        var subject = $"Active Described Vehicles {DateTime.Now:yyyy-MM-dd}";
        var content = subject; // Using subject as content by default, matching PHP implementation

        // Add each recipient - matching PHP implementation which loops through recipients
        bool allEmailsSuccessful = true;
        foreach (var toEmail in emailSettings.ToEmails)
        {
            try
            {
                // Create a new message for each recipient to match PHP's separate email sending
                var individualMessage = new MailMessage
                {
                    From = new MailAddress(emailSettings.FromEmail),
                    Subject = subject,
                    Body = content,
                    IsBodyHtml = true
                };

                individualMessage.To.Add(toEmail);
                individualMessage.Attachments.Add(new Attachment(fileName));

                await smtpClient.SendMailAsync(individualMessage);
                _logger.LogInformation("Email successfully sent to recipient: {Recipient}", toEmail);
            }
            catch (SmtpException ex)
            {
                _logger.LogError(ex, "Failed to send email to {Recipient}. SMTP error occurred: {ErrorMessage}",
                    toEmail, ex.Message);
                allEmailsSuccessful = false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred while sending email to {Recipient}: {ErrorMessage}",
                    toEmail, ex.Message);
                allEmailsSuccessful = false;
            }
        }

        return allEmailsSuccessful;
    }
}
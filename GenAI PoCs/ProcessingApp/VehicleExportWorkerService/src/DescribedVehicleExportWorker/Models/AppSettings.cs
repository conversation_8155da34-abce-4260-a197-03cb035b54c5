﻿namespace VehicleExportWorkerService.Models
{
    public class AppSettings
    {
        public required string ActiveDescVehSaveDirectory { get; set; }
        public required DataOneFtpCredentials DataOneFtpCredentials { get; set; }
        public required EmailSettings EmailSettings { get; set; }
        public required ConnectionStrings ConnectionStrings { get; set; }
    }

    public class DataOneFtpCredentials
    {
        public required string FtpServer { get; set; }
        public required string FtpUserName { get; set; }
        public required string FtpPassword { get; set; }
        public string? SubDirectory { get; set; }
    }

    public class EmailSettings
    {
        public required string SmtpServer { get; set; }
        public required int SmtpPort { get; set; }
        public required string SmtpUser { get; set; }
        public required string SmtpPassword { get; set; }
        public required string FromEmail { get; set; }
        public required string[] ToEmails { get; set; }
    }

    public class ConnectionStrings
    {
        public required string DefaultConnection { get; set; }
    }
}
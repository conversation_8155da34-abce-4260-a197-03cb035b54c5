﻿namespace VehicleExportWorkerService.Models
{
    public class ActiveDescVehicle
    {
        public int DescVehicleID { get; set; }
        public string Division { get; set; }
        public int Year { get; set; }
        public string Model { get; set; }
        public string ManufactModelCode { get; set; }
        public string Trim { get; set; }
        public string Package { get; set; }
        public string Body { get; set; }
        public string Doors { get; set; }
        public string DriveType { get; set; }
        public string BlockType { get; set; }
        public string Cylinders { get; set; }
        public string Aspiration { get; set; }
        public string Induction { get; set; }
        public string Trans { get; set; }
        public string TranSpds { get; set; }
        public string BedLength { get; set; }
        public string WheelBase { get; set; }
        public string OptionGroup { get; set; }
        public string EngSize { get; set; }
        public string Fuel { get; set; }
        public string MfrEngCode { get; set; }
        public string MfrTransCode { get; set; }
        public string Style_Name { get; set; }
    }
}

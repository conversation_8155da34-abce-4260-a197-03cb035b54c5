﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="NewFolder\**" />
    <EmbeddedResource Remove="NewFolder\**" />
    <None Remove="NewFolder\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.6.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\VehicleExportWorkerService\VehicleExportWorkerService.csproj" />
  </ItemGroup>

</Project>
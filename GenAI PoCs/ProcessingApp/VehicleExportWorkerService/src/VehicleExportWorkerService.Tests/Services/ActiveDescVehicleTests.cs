﻿using VehicleExportWorkerService.Models;
using Xunit;

namespace VehicleExportWorkerService.Tests.Models
{
    public class ActiveDescVehicleTests
    {
        [Fact]
        public void ActiveDescVehicle_ShouldSetPropertiesCorrectly()
        {
            // Arrange
            var vehicle = new ActiveDescVehicle
            {
                DescVehicleID = 1,
                Division = "Division1",
                Year = 2021,
                Model = "Model1",
                ManufactModelCode = "Code1",
                Trim = "Trim1",
                Package = "Package1",
                Body = "Body1",
                Doors = "4",
                DriveType = "AWD",
                BlockType = "V6",
                Cylinders = "6",
                Aspiration = "Turbo",
                Induction = "Direct",
                Trans = "Automatic",
                TranSpds = "6",
                BedLength = "Short",
                WheelBase = "120",
                OptionGroup = "Group1",
                EngSize = "3.5L",
                Fuel = "Gasoline",
                MfrEngCode = "MEC1",
                MfrTransCode = "MTC1",
                Style_Name = "SUV"
            };

            // Assert
            Assert.Equal(1, vehicle.DescVehicleID);
            Assert.Equal("Division1", vehicle.Division);
            Assert.Equal(2021, vehicle.Year);
            Assert.Equal("Model1", vehicle.Model);
            Assert.Equal("Code1", vehicle.ManufactModelCode);
            Assert.Equal("Trim1", vehicle.Trim);
            Assert.Equal("Package1", vehicle.Package);
            Assert.Equal("Body1", vehicle.Body);
            Assert.Equal("4", vehicle.Doors);
            Assert.Equal("AWD", vehicle.DriveType);
            Assert.Equal("V6", vehicle.BlockType);
            Assert.Equal("6", vehicle.Cylinders);
            Assert.Equal("Turbo", vehicle.Aspiration);
            Assert.Equal("Direct", vehicle.Induction);
            Assert.Equal("Automatic", vehicle.Trans);
            Assert.Equal("6", vehicle.TranSpds);
            Assert.Equal("Short", vehicle.BedLength);
            Assert.Equal("120", vehicle.WheelBase);
            Assert.Equal("Group1", vehicle.OptionGroup);
            Assert.Equal("3.5L", vehicle.EngSize);
            Assert.Equal("Gasoline", vehicle.Fuel);
            Assert.Equal("MEC1", vehicle.MfrEngCode);
            Assert.Equal("MTC1", vehicle.MfrTransCode);
            Assert.Equal("SUV", vehicle.Style_Name);
        }
    }
}

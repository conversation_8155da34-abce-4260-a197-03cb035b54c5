﻿using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Moq;
using VehicleExportWorkerService.Data;
using VehicleExportWorkerService.Models;
using Xunit;

namespace VehicleExportWorkerService.Tests.Data
{
    public class AisDataDaoTests
    {
        private readonly Mock<IDbConnection> _dbConnectionMock;
        private readonly Mock<ILogger<AisDataDao>> _loggerMock;
        private readonly AisDataDao _aisDataDao;

        public AisDataDaoTests()
        {
            _dbConnectionMock = new Mock<IDbConnection>();
            _loggerMock = new Mock<ILogger<AisDataDao>>();
            _aisDataDao = new AisDataDao(_dbConnectionMock.Object, _loggerMock.Object);
        }

        [Fact]
        public async Task TestConnectionAsync_ShouldLogInformation_WhenConnectionIsSuccessful()
        {
            // Arrange
            _dbConnectionMock.Setup(db => db.ExecuteScalarAsync<string>("SELECT 1", null, null, null, null))
                .ReturnsAsync("1");

            // Act
            await _aisDataDao.TestConnectionAsync();

            // Assert
            _loggerMock.Verify(logger => logger.LogInformation("Testing database connection..."), Times.Once);
            _loggerMock.Verify(logger => logger.LogInformation("Database connection test successful. Result: {Result}", "1"), Times.Once);
        }

        [Fact]
        public async Task TestConnectionAsync_ShouldLogError_WhenConnectionFails()
        {
            // Arrange
            var exception = new System.Exception("Connection failed");
            _dbConnectionMock.Setup(db => db.ExecuteScalarAsync<string>("SELECT 1", It.IsAny<object>(), null, null, null))
                .ThrowsAsync(exception);

            // Act & Assert
            await Assert.ThrowsAsync<System.Exception>(() => _aisDataDao.TestConnectionAsync());
            _loggerMock.Verify(logger => logger.LogError(exception, "Database connection test failed."), Times.Once);
        }

        [Fact]
        public async Task GetAllActiveDescVehiclesAsync_ShouldReturnListOfActiveDescVehicles()
        {
            // Arrange
            var expectedVehicles = new List<ActiveDescVehicle>
            {
                new ActiveDescVehicle { DescVehicleID = 1, Division = "Division1", Year = 2021, Model = "Model1" },
                new ActiveDescVehicle { DescVehicleID = 2, Division = "Division2", Year = 2022, Model = "Model2" }
            };

            _dbConnectionMock.Setup(db => db.QueryAsync<ActiveDescVehicle>(It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedVehicles);

            // Act
            var result = await _aisDataDao.GetAllActiveDescVehiclesAsync();

            // Assert
            Assert.Equal(expectedVehicles, result);
        }
    }
}

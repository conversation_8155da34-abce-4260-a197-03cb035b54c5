﻿using Moq;
using Xunit;
using Microsoft.Extensions.Logging;
using VehicleExportWorkerService.Services;
using VehicleExportWorkerService.Data;

namespace VehicleExportWorkerService.Tests.Services
{
    public class ActiveDescVehExportCoreTests
    {
        [Fact]
        public void StartTimer_ShouldSetLastTimerSuccessfully()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<ActiveDescVehExport>>();
            var daoMock = new Mock<AisDataDao>(null, null); // Mocking the DAO to exclude DB logic
            var export = new ActiveDescVehExport(loggerMock.Object, daoMock.Object);

            // Act
            export.StartTimer();

            // Assert
            Assert.NotEqual(default, export.LastTimer);
        }

        [Fact]
        public void GetCurrentTime_ShouldReturnRecentDateTime()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<ActiveDescVehExport>>();
            var daoMock = new Mock<AisDataDao>(null, null);
            var export = new ActiveDescVehExport(loggerMock.Object, daoMock.Object);

            // Act
            var current = export.GetCurrentTime();

            // Assert
            Assert.True((System.DateTime.Now - current).TotalSeconds < 2);
        }
    }
}
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("residual_vehicle_groups")]
    public class ResidualVehicleGroup
    {
        [Key]
        [Column("residual_vehicle_group_id")]
        public int residual_vehicle_group_id { get; set; }
        
        [Column("vehicle_group_year")]
        [StringLength(10)]
        public string? vehicle_group_year { get; set; } = string.Empty;
        
        [Column("vehicle_group_name")]
        [StringLength(128)]
        public string? vehicle_group_name { get; set; } = string.Empty;
        
        [Column("algmodelcode")]
        public int? algmodelcode { get; set; }
        
        [Column("tblManufactDivisions_DivisionID")]
        public int? DivisionID { get; set; }
        
        [ForeignKey("DivisionID")]
        public virtual Division? Division { get; set; }
    }
}
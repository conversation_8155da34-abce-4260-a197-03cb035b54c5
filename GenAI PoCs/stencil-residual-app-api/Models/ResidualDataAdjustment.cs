using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("residual_data_adjustment")]
    public class ResidualDataAdjustment
    {
        [Key]
        [Column("residual_adj_id")]
        public int residual_data_adjustment_id { get; set; }
        
        [Column("adjustment_amount")]
        public int adjustment_value { get; set; }

        [Column("msrp_adjustment")]
        public int msrp { get; set; }

        [Column("mrm_adjustment")]
        public string? mrm { get; set; }

        [Column("residual_vehicle_group_data_res_veh_data_id")]
        public int ResidualVehicleGroupDataID { get; set; }

        [Column("residual_eq_adjustment_codes_residual_eq_adjust_code_id")]
        public int ResidualAdjustCode { get; set; }

        [Column("tblProgramTerms_PgmTermID")]
        public int PgmTermID { get; set; }
        
        // Navigation properties
        [ForeignKey("ResidualVehicleGroupDataID")]
        public virtual ResidualVehicleGroupData? ResidualVehicleGroupData { get; set; }
        
        [ForeignKey("PgmTermID")]
        public virtual ProgramTerm? ProgramTerm { get; set; }
    }
}
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace stencil_residual_app_api.Models
{
    [Table("tblManufacturers")]
    public class Manufacturer
    {
        [Key]
        [Column("ManufactID")]
        public int ManufactID { get; set; }
        
        [Required]
        [Column("ManufactName")]
        [StringLength(100)]
        public string ManufactName { get; set; } = string.Empty;
        
        // Navigation property for divisions
        public virtual ICollection<Division> Divisions { get; set; } = new List<Division>();
    }
}
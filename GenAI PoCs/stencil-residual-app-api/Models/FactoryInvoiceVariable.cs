using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("factory_invoice_variables")]
    public class FactoryInvoiceVariable
    {
        [Key]
        [Column("factory_inv_var_id")]
        public int factory_inv_var_id { get; set; }
        
        [Column("factory_invoice_percent")]
        public decimal factory_invoice_percent { get; set; }
        
        [Column("holdback_percent")]
        public decimal holdback_percent { get; set; }
        
        [Column("advertising_percent")]
        public decimal advertising_percent { get; set; }
        
        [Column("floor_plan_days")]
        public int floor_plan_days { get; set; }
        
        [Column("floor_plan_percentage")]
        public decimal floor_plan_percentage { get; set; }
        
        [Column("prep_percent")]
        public decimal prep_percent { get; set; }
        
        [Required]
        [Column("tblManufactDivisions_DivisionID")]
        public int DivisionID { get; set; }
        
        [ForeignKey("DivisionID")]
        public virtual Division? Division { get; set; }
    }
}
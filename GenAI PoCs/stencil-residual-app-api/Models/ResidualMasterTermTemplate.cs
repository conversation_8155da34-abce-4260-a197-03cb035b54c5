using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("residual_master_term_template")]
    public class ResidualMasterTermTemplate
    {
        [Key]
        [Column("res_master_term_template")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("residual_master_res_mast_id")]
        public int ResidualMasterID { get; set; }

        [Column("tblProgramTerms_PgmTermID")]
        public int PgmTermID { get; set; }

        // Navigation properties
        [ForeignKey("ResidualMasterID")]
        [InverseProperty("ResidualMasterTermTemplates")]
        public virtual ResidualMaster? ResidualMaster { get; set; }

        [ForeignKey("PgmTermID")]
        public virtual ProgramTerm? ProgramTerm { get; set; }
    }
}
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("residual_vehicle_group_data")]
    public class ResidualVehicleGroupData
    {
        [Key]
        [Column("res_veh_data_id")]
        public int res_veh_data_id { get; set; }
        
        [Column("msrp")]
        public int msrp { get; set; }
        
        [Column("factory_invoice")]
        public int factory_invoice { get; set; }
        
        [Column("Trans")]
        [StringLength(1)]
        public string? Trans { get; set; }
        
        [Column("AC")]
        [StringLength(1)]
        public string? AC { get; set; }
        
        [Column("CC")]
        [StringLength(1)]
        public string? CC { get; set; }
        
        [Column("LS")]
        [StringLength(1)]
        public string? LS { get; set; }
        
        [Column("MR")]
        [StringLength(1)]
        public string? MR { get; set; }
        
        [Column("PL")]
        [StringLength(1)]
        public string? PL { get; set; }
        
        [Column("PS")]
        [StringLength(1)]
        public string? PS { get; set; }
        
        [Column("PW")]
        [StringLength(1)]
        public string? PW { get; set; }
        
        [Column("HFS")]
        [StringLength(1)]
        public string? HFS { get; set; }
        
        [Column("ESC")]
        [StringLength(1)]
        public string? ESC { get; set; }
        
        [Column("SIA")]
        [StringLength(1)]
        public string? SIA { get; set; }
        
        [Column("PA")]
        [StringLength(1)]
        public string? PA { get; set; }
        
        [Column("RES")]
        [StringLength(1)]
        public string? RES { get; set; }
        
        [Column("AW")]
        [StringLength(1)]
        public string? AW { get; set; }
        
        [Column("4WD")]
        [StringLength(1)]
        public string? FourWD { get; set; }
        
        [Column("RAC")]
        [StringLength(1)]
        public string? RAC { get; set; }
        
        [Column("CDC")]
        [StringLength(1)]
        public string? CDC { get; set; }
        
        [Column("Nav")]
        [StringLength(1)]
        public string? Nav { get; set; }
        
        [Column("ABS")]
        [StringLength(1)]
        public string? ABS { get; set; }
        
        [Column("tblstatus_StatusID")]
        public int tblstatus_StatusID { get; set; }
        
        [Column("residual_master_res_mast_id")]
        public int ResidualMasterID { get; set; }
        
        [Column("residual_vehicle_groups_residual_vehicle_group_id")]
        public int ResidualVehicleGroupID { get; set; }
        
        [Column("factory_invoice_variables_factory_inv_var_id")]
        public int factory_inv_var_id { get; set; }
        
        // Navigation properties
        [ForeignKey("ResidualMasterID")]
        public virtual ResidualMaster? ResidualMaster { get; set; }
        
        [ForeignKey("ResidualVehicleGroupID")]
        public virtual ResidualVehicleGroup? ResidualVehicleGroup { get; set; }
        
        [ForeignKey("factory_inv_var_id")]
        public virtual FactoryInvoiceVariable? FactoryInvoiceVariable { get; set; }
        
        public virtual ICollection<ResidualValue> ResidualValues { get; set; } = new List<ResidualValue>();
        
        public virtual ICollection<ResidualDataAdjustment> ResidualDataAdjustments { get; set; } = new List<ResidualDataAdjustment>();
    }
}
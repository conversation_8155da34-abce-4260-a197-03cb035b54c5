using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("tblFinanceCompanies")]
    public class FinanceCompany
    {
        [Key]
        [Column("FiCoID")]
        public int FiCoID { get; set; }
        
        [Required]
        [Column("FiCoName")]
        [StringLength(100)]
        public string FiCoName { get; set; } = string.Empty;
    }
}
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("residual_master")]
    public class ResidualMaster
    {
        [Key]
        [Column("res_mast_id")]
        public int res_mast_id { get; set; }

        [Required]
        [Column("record_title")]
        [StringLength(100)]
        public string record_title { get; set; } = string.Empty;

        [Required]
        [Column("model_year")]
        [StringLength(10)]
        public string model_year { get; set; } = string.Empty;

        [Required]
        [Column("new_used")]
        public int new_used { get; set; } // 0 = New, 1 = Used

        [Column("startdate")]
        public DateTime? startdate { get; set; }

        [Column("stopdate")]
        public DateTime? stopdate { get; set; }

        [Required]
        [Column("tblstatus_StatusID")]
        public int tblstatus_StatusID { get; set; } // 3 = Active, 5 = Archived, 6 = Pending

        [Required]
        [Column("data_source")]
        [StringLength(50)]
        public string data_source { get; set; } = string.Empty;

        [Column("filename")]
        [StringLength(255)]
        public string? filename { get; set; }

        [Column("tblFinanceCompanies_FiCoID")]
        public int? FiCoID { get; set; }

        [Column("vehicle_group_source")]
        [StringLength(50)]
        public string? vehicle_group_source { get; set; }

        [Column("tblCountries_CountryID")]
        public int? CountryID { get; set; }

        // Navigation properties
        [ForeignKey("FiCoID")]
        public virtual FinanceCompany? FinanceCompany { get; set; }

        [InverseProperty("ResidualMaster")]
        public virtual ICollection<ResidualDivision> ResidualDivisions { get; set; } = new List<ResidualDivision>();

        public virtual ICollection<ResidualVehicleGroupData> ResidualVehicleGroupData { get; set; } = new List<ResidualVehicleGroupData>();

        public virtual ICollection<ResidualMasterTermTemplate> ResidualMasterTermTemplates { get; set; } = new List<ResidualMasterTermTemplate>();
    }
}
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("residual_divisions")]
    public class ResidualDivision
    {
        // This entity has a composite key of DivisionID and ResidualMasterID
        // The actual database table doesn't have an 'id' column

        [Key]
        [Column("tblManufactDivisions_DivisionID")]
        public int DivisionID { get; set; }

        [Key]
        [Column("residual_master_res_mast_id")]
        public int ResidualMasterID { get; set; }

        // Navigation properties
        [ForeignKey("DivisionID")]
        public virtual Division? Division { get; set; }

        [ForeignKey("ResidualMasterID")]
        [InverseProperty("ResidualDivisions")]
        public virtual ResidualMaster? ResidualMaster { get; set; }
    }
}
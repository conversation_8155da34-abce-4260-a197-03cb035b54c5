using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("residual_value")]
    public class ResidualValue
    {
        [Key]
        [Column("residual_value_id")]
        public int residual_value_id { get; set; }

        [Column("value")]
        public string value { get; set; }

        [Column("value_type")]
        public int value_type { get; set; } // 0 = Rate, 1 = MSRP

        [Column("tblProgramTerms_PgmTermID")]
        public int PgmTermID { get; set; }

        [Column("residual_vehicle_group_data_res_veh_data_id")]
        public int ResidualVehicleGroupDataID { get; set; }

        // Navigation properties
        [ForeignKey("PgmTermID")]
        public virtual ProgramTerm? ProgramTerm { get; set; }

        [ForeignKey("ResidualVehicleGroupDataID")]
        public virtual ResidualVehicleGroupData? ResidualVehicleGroupData { get; set; }
    }
}
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace stencil_residual_app_api.Models
{
    [Table("tblManufactDivisions")]
    public class Division
    {
        [Key]
        [Column("DivisionID")]
        public int DivisionID { get; set; }
        
        [Required]
        [Column("DivName")]
        [StringLength(100)]
        public string? DivName { get; set; } = string.Empty;
        
        [Column("tblManufacturers_ManufactID")]
        public int? ManufacturerID { get; set; }

        // Navigation property for the manufacturer
        [ForeignKey("ManufacturerID")]
        public virtual Manufacturer? Manufacturer { get; set; }

        // Adding this property to fix build errors - it may be referenced in controllers
        [NotMapped]
        [StringLength(100)]
        public string? ManufactName { get; set; } = string.Empty;
        
        // This is required for EF seeding but isn't actually in the database table
        [NotMapped]
        public int ResidualMasterID { get; set; }
    }
}
﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace stencil_residual_app_api.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DataSources",
                columns: table => new
                {
                    data_source = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataSources", x => x.data_source);
                });

            migrationBuilder.CreateTable(
                name: "FinanceCompanies",
                columns: table => new
                {
                    FiCoID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FiCoName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    tblstatus_StatusID = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FinanceCompanies", x => x.FiCoID);
                });

            migrationBuilder.CreateTable(
                name: "ResidualMasters",
                columns: table => new
                {
                    res_mast_id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    record_title = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    model_year = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    new_used = table.Column<int>(type: "int", nullable: false),
                    startdate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    stopdate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    tblstatus_StatusID = table.Column<int>(type: "int", nullable: false),
                    data_source = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    filename = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    FiCoID = table.Column<int>(type: "int", nullable: true),
                    FiCoName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    vehicle_group_source = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResidualMasters", x => x.res_mast_id);
                });

            migrationBuilder.CreateTable(
                name: "Divisions",
                columns: table => new
                {
                    DivisionID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DivName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ManufactName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ResidualMasterID = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Divisions", x => x.DivisionID);
                    table.ForeignKey(
                        name: "FK_Divisions_ResidualMasters_ResidualMasterID",
                        column: x => x.ResidualMasterID,
                        principalTable: "ResidualMasters",
                        principalColumn: "res_mast_id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "DataSources",
                column: "data_source",
                values: new object[]
                {
                    "AIS",
                    "ALG"
                });

            migrationBuilder.InsertData(
                table: "FinanceCompanies",
                columns: new[] { "FiCoID", "FiCoName", "tblstatus_StatusID" },
                values: new object[,]
                {
                    { 1, "Chase", 3 },
                    { 2, "Bank of America", 6 },
                    { 3, "US Bank", 3 },
                    { 4, "Capital One", 3 }
                });

            migrationBuilder.InsertData(
                table: "ResidualMasters",
                columns: new[] { "res_mast_id", "FiCoID", "FiCoName", "data_source", "filename", "model_year", "new_used", "record_title", "startdate", "stopdate", "tblstatus_StatusID", "vehicle_group_source" },
                values: new object[,]
                {
                    { 1, 1, null, "ALG", null, "2025", 0, "Chase Residual Values", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), 3, null },
                    { 2, 2, null, "ALG", null, "2025", 0, "Bank of America Residual Values", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified), 6, null },
                    { 3, 3, null, "AIS", null, "2025", 1, "US Bank Residual Values", new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2025, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified), 3, null },
                    { 4, 4, null, "ALG", null, "2024", 0, "Capital One Residual Values", new DateTime(2024, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2025, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified), 5, null }
                });

            migrationBuilder.InsertData(
                table: "Divisions",
                columns: new[] { "DivisionID", "DivName", "ManufactName", "ResidualMasterID" },
                values: new object[,]
                {
                    { 101, "Toyota", "Toyota Motor Corporation", 1 },
                    { 102, "Lexus", "Toyota Motor Corporation", 1 },
                    { 103, "Honda", "Honda Motor Co.", 2 },
                    { 104, "Acura", "Honda Motor Co.", 2 },
                    { 105, "BMW", "BMW Group", 3 },
                    { 106, "Ford", "Ford Motor Company", 4 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Divisions_ResidualMasterID",
                table: "Divisions",
                column: "ResidualMasterID");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataSources");

            migrationBuilder.DropTable(
                name: "Divisions");

            migrationBuilder.DropTable(
                name: "FinanceCompanies");

            migrationBuilder.DropTable(
                name: "ResidualMasters");
        }
    }
}

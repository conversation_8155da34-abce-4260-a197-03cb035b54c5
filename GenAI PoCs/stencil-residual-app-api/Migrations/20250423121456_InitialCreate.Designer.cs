﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using stencil_residual_app_api.Data;

#nullable disable

namespace stencil_residual_app_api.Migrations
{
    [DbContext(typeof(ResidualAppDbContext))]
    [Migration("20250423121456_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("stencil_residual_app_api.Models.DataSource", b =>
                {
                    b.Property<string>("data_source")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("data_source");

                    b.ToTable("DataSources");

                    b.HasData(
                        new
                        {
                            data_source = "ALG"
                        },
                        new
                        {
                            data_source = "AIS"
                        });
                });

            modelBuilder.Entity("stencil_residual_app_api.Models.Division", b =>
                {
                    b.Property<int>("DivisionID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DivisionID"));

                    b.Property<string>("DivName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ManufactName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ResidualMasterID")
                        .HasColumnType("int");

                    b.HasKey("DivisionID");

                    b.HasIndex("ResidualMasterID");

                    b.ToTable("Divisions");

                    b.HasData(
                        new
                        {
                            DivisionID = 101,
                            DivName = "Toyota",
                            ManufactName = "Toyota Motor Corporation",
                            ResidualMasterID = 1
                        },
                        new
                        {
                            DivisionID = 102,
                            DivName = "Lexus",
                            ManufactName = "Toyota Motor Corporation",
                            ResidualMasterID = 1
                        },
                        new
                        {
                            DivisionID = 103,
                            DivName = "Honda",
                            ManufactName = "Honda Motor Co.",
                            ResidualMasterID = 2
                        },
                        new
                        {
                            DivisionID = 104,
                            DivName = "Acura",
                            ManufactName = "Honda Motor Co.",
                            ResidualMasterID = 2
                        },
                        new
                        {
                            DivisionID = 105,
                            DivName = "BMW",
                            ManufactName = "BMW Group",
                            ResidualMasterID = 3
                        },
                        new
                        {
                            DivisionID = 106,
                            DivName = "Ford",
                            ManufactName = "Ford Motor Company",
                            ResidualMasterID = 4
                        });
                });

            modelBuilder.Entity("stencil_residual_app_api.Models.FinanceCompany", b =>
                {
                    b.Property<int>("FiCoID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FiCoID"));

                    b.Property<string>("FiCoName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("tblstatus_StatusID")
                        .HasColumnType("int");

                    b.HasKey("FiCoID");

                    b.ToTable("FinanceCompanies");

                    b.HasData(
                        new
                        {
                            FiCoID = 1,
                            FiCoName = "Chase",
                            tblstatus_StatusID = 3
                        },
                        new
                        {
                            FiCoID = 2,
                            FiCoName = "Bank of America",
                            tblstatus_StatusID = 6
                        },
                        new
                        {
                            FiCoID = 3,
                            FiCoName = "US Bank",
                            tblstatus_StatusID = 3
                        },
                        new
                        {
                            FiCoID = 4,
                            FiCoName = "Capital One",
                            tblstatus_StatusID = 3
                        });
                });

            modelBuilder.Entity("stencil_residual_app_api.Models.ResidualMaster", b =>
                {
                    b.Property<int>("res_mast_id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("res_mast_id"));

                    b.Property<int?>("FiCoID")
                        .HasColumnType("int");

                    b.Property<string>("FiCoName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("data_source")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("filename")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("model_year")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("new_used")
                        .HasColumnType("int");

                    b.Property<string>("record_title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("startdate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("stopdate")
                        .HasColumnType("datetime2");

                    b.Property<int>("tblstatus_StatusID")
                        .HasColumnType("int");

                    b.Property<string>("vehicle_group_source")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("res_mast_id");

                    b.ToTable("ResidualMasters");

                    b.HasData(
                        new
                        {
                            res_mast_id = 1,
                            FiCoID = 1,
                            data_source = "ALG",
                            model_year = "2025",
                            new_used = 0,
                            record_title = "Chase Residual Values",
                            startdate = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            stopdate = new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            tblstatus_StatusID = 3
                        },
                        new
                        {
                            res_mast_id = 2,
                            FiCoID = 2,
                            data_source = "ALG",
                            model_year = "2025",
                            new_used = 0,
                            record_title = "Bank of America Residual Values",
                            startdate = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            stopdate = new DateTime(2025, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            tblstatus_StatusID = 6
                        },
                        new
                        {
                            res_mast_id = 3,
                            FiCoID = 3,
                            data_source = "AIS",
                            model_year = "2025",
                            new_used = 1,
                            record_title = "US Bank Residual Values",
                            startdate = new DateTime(2025, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            stopdate = new DateTime(2025, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            tblstatus_StatusID = 3
                        },
                        new
                        {
                            res_mast_id = 4,
                            FiCoID = 4,
                            data_source = "ALG",
                            model_year = "2024",
                            new_used = 0,
                            record_title = "Capital One Residual Values",
                            startdate = new DateTime(2024, 7, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            stopdate = new DateTime(2025, 6, 30, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            tblstatus_StatusID = 5
                        });
                });

            modelBuilder.Entity("stencil_residual_app_api.Models.Division", b =>
                {
                    b.HasOne("stencil_residual_app_api.Models.ResidualMaster", "ResidualMaster")
                        .WithMany("Divisions")
                        .HasForeignKey("ResidualMasterID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ResidualMaster");
                });

            modelBuilder.Entity("stencil_residual_app_api.Models.ResidualMaster", b =>
                {
                    b.Navigation("Divisions");
                });
#pragma warning restore 612, 618
        }
    }
}

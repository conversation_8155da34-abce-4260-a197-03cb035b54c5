using Microsoft.EntityFrameworkCore;
using stencil_residual_app_api.Models;

namespace stencil_residual_app_api.Data
{
    public class ResidualAppDbContext : DbContext
    {
        public ResidualAppDbContext(DbContextOptions<ResidualAppDbContext> options)
            : base(options)
        {
        }

        public DbSet<ResidualMaster> ResidualMasters { get; set; }
        public DbSet<Division> Divisions { get; set; }
        public DbSet<Manufacturer> Manufacturers { get; set; }
        public DbSet<FinanceCompany> FinanceCompanies { get; set; }
        public DbSet<DataSource> DataSources { get; set; }
        public DbSet<ResidualVehicleGroupData> ResidualVehicleGroupData { get; set; }
        public DbSet<ResidualVehicleGroup> ResidualVehicleGroups { get; set; }
        public DbSet<ResidualValue> ResidualValues { get; set; }
        public DbSet<ProgramTerm> ProgramTerms { get; set; }
        public DbSet<ResidualDivision> ResidualDivisions { get; set; }
        public DbSet<ResidualMasterTermTemplate> ResidualMasterTermTemplates { get; set; }
        public DbSet<ResidualDataAdjustment> ResidualDataAdjustments { get; set; }
        public DbSet<FactoryInvoiceVariable> FactoryInvoiceVariables { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure relationships and mappings to match existing database
            
            // Map tables to match PHP code table names
            //modelBuilder.Entity<ResidualMaster>().ToTable("residual_master");
            //modelBuilder.Entity<Division>().ToTable("tblManufactDivisions");
            //modelBuilder.Entity<Manufacturer>().ToTable("tblManufacturers");
            //modelBuilder.Entity<FinanceCompany>().ToTable("tblFinanceCompanies");
            // ...existing code...

            // Configure relationship between Division and Manufacturer
            modelBuilder.Entity<Division>()
                .HasOne(d => d.Manufacturer)
                .WithMany(m => m.Divisions)
                .HasForeignKey(d => d.ManufacturerID);

            modelBuilder.Entity<ResidualDivision>()
            .HasKey(a => new { a.DivisionID, a.ResidualMasterID });


            // Seed Manufacturers first
            modelBuilder.Entity<Manufacturer>().HasData(
                new Manufacturer { ManufactID = 1, ManufactName = "Toyota Motor Corporation" },
                new Manufacturer { ManufactID = 2, ManufactName = "Honda Motor Co." },
                new Manufacturer { ManufactID = 3, ManufactName = "BMW Group" },
                new Manufacturer { ManufactID = 4, ManufactName = "Ford Motor Company" }
            );

            // Then seed Divisions with references to Manufacturers
            modelBuilder.Entity<Division>().HasData(
                new Division { DivisionID = 101, DivName = "Toyota", ManufacturerID = 1, ResidualMasterID = 1 },
                new Division { DivisionID = 102, DivName = "Lexus", ManufacturerID = 1, ResidualMasterID = 1 },
                new Division { DivisionID = 103, DivName = "Honda", ManufacturerID = 2, ResidualMasterID = 2 },
                new Division { DivisionID = 104, DivName = "Acura", ManufacturerID = 2, ResidualMasterID = 2 },
                new Division { DivisionID = 105, DivName = "BMW", ManufacturerID = 3, ResidualMasterID = 3 },
                new Division { DivisionID = 106, DivName = "Ford", ManufacturerID = 4, ResidualMasterID = 4 }
            );

            // ...existing code...
        }

        // ...existing code...
    }
}
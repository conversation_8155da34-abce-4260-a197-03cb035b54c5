using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using stencil_residual_app_api.Data;
using stencil_residual_app_api.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace stencil_residual_app_api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ResidualMastersController : ControllerBase
    {
        private readonly ResidualAppDbContext _context;

        public ResidualMastersController(ResidualAppDbContext context)
        {
            _context = context;
        }

        // GET: api/ResidualMasters
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ResidualMaster>>> GetResidualMasters()
        {
            return await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
                .Include(rm => rm.FinanceCompany)
                .ToListAsync();
        }

        // GET: api/ResidualMasters/active
        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<ResidualMaster>>> GetActiveResidualMasters()
        {
            var today = DateTime.Today;
            
            return await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
                .Include(rm => rm.FinanceCompany)
                .Where(rm => rm.tblstatus_StatusID == 3 && // Active status
                            rm.startdate <= today && 
                            rm.stopdate >= today)
                .OrderByDescending(rm => rm.startdate)
                .ToListAsync();
        }

        // GET: api/ResidualMasters/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ResidualMaster>> GetResidualMaster(int id)
        {
            var residualMaster = await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
                .Include(rm => rm.FinanceCompany)
                .Include(rm => rm.ResidualMasterTermTemplates)
                    .ThenInclude(tmpl => tmpl.ProgramTerm)
                .Include(rm => rm.ResidualVehicleGroupData)
                 .ThenInclude(rvgds => rvgds.ResidualDataAdjustments)
                   .Include(rm => rm.ResidualVehicleGroupData)
                    .ThenInclude(rvgd => rvgd.ResidualVehicleGroup)
                .Include(rm => rm.ResidualVehicleGroupData)
                    .ThenInclude(rvgd => rvgd.ResidualValues)
                        .ThenInclude(rv => rv.ProgramTerm)
                .FirstOrDefaultAsync(rm => rm.res_mast_id == id);

            if (residualMaster == null)
            {
                return NotFound();
            }

            return residualMaster;
        }

        // GET: api/ResidualMasters/dataSource/ALG
        [HttpGet("dataSource/{dataSource}")]
        public async Task<ActionResult<IEnumerable<ResidualMaster>>> GetResidualMastersByDataSource(string dataSource)
        {
            return await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
                .Include(rm => rm.FinanceCompany)
                .Where(rm => rm.data_source == dataSource)
                .OrderByDescending(rm => rm.startdate)
                .ToListAsync();
        }

        // GET: api/ResidualMasters/financeCompany/1
        [HttpGet("financeCompany/{fiCoId}")]
        public async Task<ActionResult<IEnumerable<ResidualMaster>>> GetResidualMastersByFinanceCompany(int fiCoId)
        {
            return await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
                .Include(rm => rm.FinanceCompany)
                .Where(rm => rm.FiCoID == fiCoId)
                .OrderByDescending(rm => rm.startdate)
                .ToListAsync();
        }

        // GET: api/ResidualMasters/dataSource/{dataSource}/financeCompany/{fiCoId}
        [HttpGet("dataSource/{dataSource}/financeCompany/{fiCoId}")]
        public async Task<ActionResult<IEnumerable<ResidualMaster>>> GetResidualMastersByDataSourceAndFinanceCompany(string dataSource, int fiCoId)
        {
            return await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
               .Include(rm => rm.FinanceCompany)
                .Where(rm => rm.data_source == dataSource && rm.FiCoID == fiCoId)
               .OrderByDescending(rm => rm.startdate)
                .ToListAsync();
        }

        // GET: api/ResidualMasters/activeByDivision/{divisionId}
        [HttpGet("activeByDivision/{divisionId}")]
        public async Task<ActionResult<IEnumerable<ResidualMaster>>> GetActiveResidualMastersByDivision(int divisionId)
        {
            var today = DateTime.Today;
            
            return await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
                .Include(rm => rm.FinanceCompany)
                .Where(rm => rm.tblstatus_StatusID == 3 && // Active status
                            rm.startdate <= today && 
                            rm.stopdate >= today &&
                            rm.ResidualDivisions.Any(rd => rd.DivisionID == divisionId))
                .OrderByDescending(rm => rm.startdate)
                .ToListAsync();
        }

        // PUT: api/ResidualMasters/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutResidualMaster(int id, ResidualMaster residualMaster)
        {
            if (id != residualMaster.res_mast_id)
            {
                return BadRequest();
            }

            // Disconnect existing relationships to avoid conflicts
            var existingMaster = await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                .FirstOrDefaultAsync(rm => rm.res_mast_id == id);

            if (existingMaster == null)
            {
                return NotFound();
            }

            // Remove existing division relationships
            _context.ResidualDivisions.RemoveRange(existingMaster.ResidualDivisions);
            
            // Update the entity
            _context.Entry(existingMaster).CurrentValues.SetValues(residualMaster);
            
            // Add the new division relationships
            foreach (var division in residualMaster.ResidualDivisions)
            {
                _context.ResidualDivisions.Add(new ResidualDivision
                {
                    ResidualMasterID = id,
                    DivisionID = division.DivisionID
                });
            }

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ResidualMasterExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/ResidualMasters
        [HttpPost]
        public async Task<ActionResult<ResidualMaster>> PostResidualMaster(ResidualMaster residualMaster)
        {
            // Detach any existing entities from the context
            var divisions = residualMaster.ResidualDivisions.ToList();
            residualMaster.ResidualDivisions.Clear();
            
            // Add the residual master
            _context.ResidualMasters.Add(residualMaster);
            await _context.SaveChangesAsync();
            
            // Add the division relationships
            foreach (var division in divisions)
            {
                _context.ResidualDivisions.Add(new ResidualDivision
                {
                    ResidualMasterID = residualMaster.res_mast_id,
                    DivisionID = division.DivisionID
                });
            }
            
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetResidualMaster", new { id = residualMaster.res_mast_id }, residualMaster);
        }

        // DELETE: api/ResidualMasters/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteResidualMaster(int id)
        {
            var residualMaster = await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                .Include(rm => rm.ResidualVehicleGroupData)
                    .ThenInclude(rvgd => rvgd.ResidualValues)
                .Include(rm => rm.ResidualVehicleGroupData)
                    .ThenInclude(rvgd => rvgd.ResidualDataAdjustments)
                .Include(rm => rm.ResidualMasterTermTemplates)
                .FirstOrDefaultAsync(rm => rm.res_mast_id == id);
                
            if (residualMaster == null)
            {
                return NotFound();
            }

            // Delete related records first
            foreach (var rvgd in residualMaster.ResidualVehicleGroupData)
            {
                _context.ResidualValues.RemoveRange(rvgd.ResidualValues);
                _context.ResidualDataAdjustments.RemoveRange(rvgd.ResidualDataAdjustments);
            }
            
            _context.ResidualVehicleGroupData.RemoveRange(residualMaster.ResidualVehicleGroupData);
            _context.ResidualDivisions.RemoveRange(residualMaster.ResidualDivisions);
            _context.ResidualMasterTermTemplates.RemoveRange(residualMaster.ResidualMasterTermTemplates);
            
            // Finally, delete the residual master
            _context.ResidualMasters.Remove(residualMaster);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        // PATCH: api/ResidualMasters/5/archive
        [HttpPatch("{id}/archive")]
        public async Task<IActionResult> ArchiveResidualMaster(int id)
        {
            var residualMaster = await _context.ResidualMasters.FindAsync(id);
            if (residualMaster == null)
            {
                return NotFound();
            }

            // Set status to Archived (5)
            residualMaster.tblstatus_StatusID = 5;
            
            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ResidualMasterExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // GET: api/ResidualMasters/byDivisionWithTerms/101
        [HttpGet("byDivisionWithTerms/{divisionId}")]
        public async Task<ActionResult<IEnumerable<object>>> GetResidualMastersByDivisionWithTerms(int divisionId)
        {
            var today = DateTime.Today;
            
            var residualMasters = await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                    .ThenInclude(rd => rd.Division)
                        .ThenInclude(d => d.Manufacturer)
                .Include(rm => rm.FinanceCompany)
                .Include(rm => rm.ResidualMasterTermTemplates)
                    .ThenInclude(tmpl => tmpl.ProgramTerm)
                .Where(rm => rm.tblstatus_StatusID == 3 && // Active status
                            rm.startdate <= today && 
                            rm.stopdate >= today &&
                            rm.ResidualDivisions.Any(rd => rd.DivisionID == divisionId))
                .OrderByDescending(rm => rm.startdate)
                .ToListAsync();

            // Transform the data to match the expected format from the PHP application
            var result = residualMasters.Select(rm => new {
                rm.res_mast_id,
                rm.record_title,
                rm.model_year,
                Finance_Company = rm.FinanceCompany?.FiCoName ?? "",
                data_source = rm.data_source,
                program_terms = rm.ResidualMasterTermTemplates
                    .OrderBy(t => t.ProgramTerm.PgmTermMin)
                    .Select(t => new {
                        pgm_term_id = t.PgmTermID,
                        pgm_term_min = t.ProgramTerm.PgmTermMin,
                        pgm_term_max = t.ProgramTerm.PgmTermMax
                    }).ToList()
            }).ToList();

            return Ok(result);
        }

        // POST: api/ResidualMasters/{id}/vehicles
        [HttpPost("{id}/vehicles")]
        public async Task<IActionResult> SaveResidualVehicles(int id, [FromBody] List<dynamic> vehicles)
        {
            var residualMaster = await _context.ResidualMasters
                .Include(rm => rm.ResidualVehicleGroupData)
                    .ThenInclude(rvgd => rvgd.ResidualValues)
                .Include(rm => rm.ResidualVehicleGroupData)
                    .ThenInclude(rvgd => rvgd.ResidualDataAdjustments)
                .FirstOrDefaultAsync(rm => rm.res_mast_id == id);

            if (residualMaster == null)
            {
                return NotFound($"Residual Master with ID {id} not found");
            }

            try
            {
                foreach (var vehicle in vehicles)
                {
                    var jsonElement = (JsonElement)vehicle;
                    int vehicleId = jsonElement.GetProperty("id").GetInt32();
                    string status = jsonElement.GetProperty("status").GetString();
                    int vehicleGroupId = jsonElement.GetProperty("residual_vehicle_group_id").GetInt32();
                    int msrp = jsonElement.GetProperty("msrp").GetInt32();
                    int factoryInvoice = jsonElement.GetProperty("factory_invoice").GetInt32();
                    //int vehicleId = (int)vehicle.id;
                    //string status = (string)vehicle.status;
                    //int vehicleGroupId = (int)vehicle.residual_vehicle_group_id;
                    //var msrp = Convert.ToInt32(vehicle.msrp);
                    //var factoryInvoice = Convert.ToInt32(vehicle.factory_invoice);

                    // Find existing vehicle or create a new one
                    var residualVehicleGroup = await _context.ResidualVehicleGroupData
                        .Include(rvgd => rvgd.ResidualValues)
                        .Include(rvgd => rvgd.ResidualDataAdjustments)
                        .FirstOrDefaultAsync(rvgd =>
                            rvgd.ResidualMasterID == id &&
                            rvgd.ResidualVehicleGroupID == vehicleGroupId);

                    if (residualVehicleGroup == null)
                    {
                        // Create new vehicle group data
                        residualVehicleGroup = new ResidualVehicleGroupData
                        {
                            ResidualMasterID = id,
                            ResidualVehicleGroupID = vehicleGroupId,
                            msrp = msrp,
                            factory_invoice = factoryInvoice,
                            tblstatus_StatusID = 1,
                            factory_inv_var_id = 1,
                            ResidualValues = new List<ResidualValue>(),
                            ResidualDataAdjustments = new List<ResidualDataAdjustment>()
                        };

                        _context.ResidualVehicleGroupData.Add(residualVehicleGroup);
                        await _context.SaveChangesAsync();
                    }
                    else
                    {
                        // Update existing vehicle group data
                        residualVehicleGroup.msrp = msrp;
                        residualVehicleGroup.factory_invoice = factoryInvoice;
                        residualVehicleGroup.tblstatus_StatusID = 1;
                    }

                    // Process term values
                    // Process term values
                    var termValuesElement = jsonElement.GetProperty("term_values");
                    var termValues = JsonSerializer.Deserialize<Dictionary<string, object>>(termValuesElement.GetRawText());

                    foreach (var termKvp in termValues)
                    {
                        int term = int.Parse(termKvp.Key);
                        var value = termKvp.Value.ToString();

                        // Get the program term ID for this term
                        var programTerm = await _context.ProgramTerms
                            .FirstOrDefaultAsync(pt => pt.PgmTermMin == term && pt.PgmTermMax == term);

                        if (programTerm == null)
                        {
                            continue; // Skip if term not found
                        }

                        // Find existing residual value or create new one
                        var residualValue = residualVehicleGroup.ResidualValues
                            .FirstOrDefault(rv => rv.PgmTermID == programTerm.PgmTermID);

                        if (residualValue == null)
                        {
                            // Create new residual value
                            residualValue = new ResidualValue
                            {
                                ResidualVehicleGroupDataID = residualVehicleGroup.res_veh_data_id,
                                PgmTermID = programTerm.PgmTermID,
                                value = value ?? string.Empty
                            };

                            _context.ResidualValues.Add(residualValue);
                        }
                        else
                        {
                            // Update existing residual value
                            residualValue.value = value ?? string.Empty;
                        }
                    }

                    // Process adjustments if they exist
                    //if (vehicle.adjustments != null)
                    //{
                    //    var adjustments = (IEnumerable<dynamic>)vehicle.adjustments;
                    //    foreach (var adjustment in adjustments)
                    //    {
                    //        int adjustmentId = (int)adjustment.id;
                    //        int adjustmentCodeId = (int)adjustment.residual_eq_adjust_code_id;
                    //        string description = (string)adjustment.description;
                    //        var mrmAdjustment = adjustment.mrm_adjustment;

                    //        // Skip if no adjustment code selected
                    //        if (adjustmentCodeId <= 0)
                    //        {
                    //            continue;
                    //        }

                    //        // Find or create adjustment
                    //        var existingAdjustment = residualVehicleGroup.ResidualDataAdjustments
                    //            .FirstOrDefault(rda => rda.residual_data_adjustment_id == adjustmentId);

                    //        if (existingAdjustment == null)
                    //        {
                    //            // Create new adjustment
                    //            existingAdjustment = new ResidualDataAdjustment
                    //            {
                    //                ResidualVehicleGroupDataID = residualVehicleGroup.ResidualVehicleGroupID,
                    //                residual_data_adjustment_id = adjustmentCodeId,
                    //                //Description = description,
                    //                mrm = mrmAdjustment ?? 0
                    //            };

                    //            _context.ResidualDataAdjustments.Add(existingAdjustment);
                    //            await _context.SaveChangesAsync();
                    //        }
                    //        else
                    //        {
                    //            // Update existing adjustment
                    //            existingAdjustment.residual_data_adjustment_id = adjustmentCodeId;
                    //            //existingAdjustment.Description = description;
                    //            existingAdjustment.adjustment_value = mrmAdjustment ?? 0;
                    //        }

                    //        // Process adjustment term values
                    //        var adjTerms = (IDictionary<string, object>)adjustment.terms;
                    //        foreach (var adjTermKvp in adjTerms)
                    //        {
                    //            int term = int.Parse(adjTermKvp.Key);
                    //            decimal value = Convert.ToDecimal(adjTermKvp.Value);

                    //            // Skip if adjustment value is 0
                    //            if (value == 0)
                    //            {
                    //                continue;
                    //            }

                    //            // Get the program term ID for this term
                    //            var programTerm = await _context.ProgramTerms
                    //                .FirstOrDefaultAsync(pt => pt.PgmTermMin <= term && pt.PgmTermMax >= term);

                    //            if (programTerm == null)
                    //            {
                    //                continue; // Skip if term not found
                    //            }

                    //            // Find or create adjustment term value
                    //            //var adjTermValue = await _context.ResidualAdjustmentTermValues
                    //            //    .FirstOrDefaultAsync(ratv =>
                    //            //        ratv.ResidualDataAdjustmentID == existingAdjustment.residual_data_adjust_id &&
                    //            //        ratv.PgmTermID == programTerm.pgm_term_id);

                    //            //if (adjTermValue == null)
                    //            //{
                    //            //    // Create new adjustment term value
                    //            //    adjTermValue = new ResidualAdjustmentTermValue
                    //            //    {
                    //            //        ResidualDataAdjustmentID = existingAdjustment.residual_data_adjust_id,
                    //            //        PgmTermID = programTerm.pgm_term_id,
                    //            //        AdjustmentValue = value
                    //            //    };

                    //            //    _context.ResidualAdjustmentTermValues.Add(adjTermValue);
                    //            //}
                    //            //else
                    //            //{
                    //            //    // Update existing adjustment term value
                    //            //    adjTermValue.AdjustmentValue = value;
                    //            //}
                    //        }
                    //    }
                    //}
                }

                await _context.SaveChangesAsync();
                return Ok(new { success = true, message = "Residual vehicles saved successfully" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"Error saving residual vehicles: {ex.Message}" });
            }
        }

        private bool ResidualMasterExists(int id)
        {
            return _context.ResidualMasters.Any(e => e.res_mast_id == id);
        }
    }
}
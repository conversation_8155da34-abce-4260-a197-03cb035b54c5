using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using stencil_residual_app_api.Data;
using stencil_residual_app_api.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stencil_residual_app_api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DataSourcesController : ControllerBase
    {
        private readonly ResidualAppDbContext _context;

        public DataSourcesController(ResidualAppDbContext context)
        {
            _context = context;
        }

        // GET: api/DataSources
        [HttpGet]
        public async Task<ActionResult<IEnumerable<DataSource>>> GetDataSources()
        {
            // Get distinct data_source values from the ResidualMasters table
            var distinctDataSources = await _context.ResidualMasters
                .Select(rm => rm.data_source)
                .Distinct()
                .OrderBy(ds => ds)
                .ToListAsync();

            // Convert to DataSource objects
            var result = distinctDataSources
                .Select(ds => new DataSource { data_source = ds })
                .ToList();

            return result;
        }

        // GET: api/DataSources/withActiveResiduals
        [HttpGet("withActiveResiduals")]
        public async Task<ActionResult<IEnumerable<DataSource>>> GetDataSourcesWithActiveResiduals()
        {
            var today = DateTime.Today;
            
            // Get data sources that have active residual masters
            var dataSources = await _context.ResidualMasters
                .Where(rm => rm.tblstatus_StatusID == 3 && // Active status
                            rm.startdate <= today && 
                            rm.stopdate >= today)
                .Select(rm => rm.data_source)
                .Distinct()
                .OrderBy(ds => ds)
                .ToListAsync();

            return dataSources.Select(ds => new DataSource { data_source = ds }).ToList();
        }

        // GET: api/DataSources/ALG
        [HttpGet("{id}")]
        public async Task<ActionResult<DataSource>> GetDataSource(string id)
        {
            // Look for the data source in the ResidualMasters table
            var dataSource = await _context.ResidualMasters
                .Where(rm => rm.data_source == id)
                .Select(rm => rm.data_source)
                .FirstOrDefaultAsync();

            if (dataSource == null)
            {
                return NotFound();
            }

            return new DataSource { data_source = dataSource };
        }

        // GET: api/DataSources/forDivision/101
        [HttpGet("forDivision/{divisionId}")]
        public async Task<ActionResult<IEnumerable<DataSource>>> GetDataSourcesForDivision(int divisionId)
        {
            var today = DateTime.Today;
            
            // Get data sources with active residual masters for the specified division
            var dataSources = await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                .Where(rm => rm.tblstatus_StatusID == 3 && // Active status
                            rm.startdate <= today && 
                            rm.stopdate >= today &&
                            rm.ResidualDivisions.Any(rd => rd.DivisionID == divisionId))
                .Select(rm => rm.data_source)
                .Distinct()
                .OrderBy(ds => ds)
                .ToListAsync();

            return dataSources.Select(ds => new DataSource { data_source = ds }).ToList();
        }

        // GET: api/DataSources/forFinanceCompany/1
        [HttpGet("forFinanceCompany/{ficoId}")]
        public async Task<ActionResult<IEnumerable<DataSource>>> GetDataSourcesForFinanceCompany(int ficoId)
        {
            var today = DateTime.Today;
            
            // Get data sources with active residual masters for the specified finance company
            var dataSources = await _context.ResidualMasters
                .Where(rm => rm.tblstatus_StatusID == 3 && // Active status
                            rm.startdate <= today && 
                            rm.stopdate >= today &&
                            rm.FiCoID == ficoId)
                .Select(rm => rm.data_source)
                .Distinct()
                .OrderBy(ds => ds)
                .ToListAsync();

            return dataSources.Select(ds => new DataSource { data_source = ds }).ToList();
        }
    }
}
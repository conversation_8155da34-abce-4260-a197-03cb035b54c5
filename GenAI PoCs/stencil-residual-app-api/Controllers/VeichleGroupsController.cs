﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using stencil_residual_app_api.Data;
using stencil_residual_app_api.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stencil_residual_app_api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class VehicleGroupsController : ControllerBase
    {
        private readonly ResidualAppDbContext _context;

        public VehicleGroupsController(ResidualAppDbContext context)
        {
            _context = context;
        }

        // GET: api/VehicleGroups
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ResidualVehicleGroup>>> GetVehicleGroups()
        {
            return await _context.ResidualVehicleGroups
                .Include(vg => vg.Division)
                    .ThenInclude(d => d.Manufacturer)
                .OrderBy(vg => vg.Division != null ? vg.Division.DivName : string.Empty)
                .ThenBy(vg => vg.vehicle_group_year)
                .ThenBy(vg => vg.vehicle_group_name)
                .ToListAsync();
        }

        // GET: api/VehicleGroups/forResidualMaster/{residualMasterId}
        [HttpGet("forResidualMaster/{residualMasterId}")]
        public async Task<ActionResult<IEnumerable<ResidualVehicleGroup>>> GetVehicleGroupsForResidualMaster(int residualMasterId)
        {
            // First, check if the residual master exists
            var residualMaster = await _context.ResidualMasters
                .Include(rm => rm.ResidualDivisions)
                .FirstOrDefaultAsync(rm => rm.res_mast_id == residualMasterId);

            if (residualMaster == null)
            {
                return NotFound($"Residual Master with ID {residualMasterId} not found");
            }

            // Get the division IDs associated with this residual master
            var divisionIds = residualMaster.ResidualDivisions?
                .Select(rd => rd.DivisionID)
                .ToList() ?? new List<int>();

            // Fetch all vehicle groups and perform the filtering on the client side
            var vehicleGroups = await _context.ResidualVehicleGroups
                .Where(c=>c.algmodelcode == 170)
                .ToListAsync(); // First fetch all the data

            // Now filter on the client side
            var filteredVehicleGroups = vehicleGroups
                .Where(vg => divisionIds.Contains(vg.DivisionID ?? 0))
                .OrderBy(vg => vg.Division != null ? vg.Division.DivName : string.Empty)
                .ThenBy(vg => vg.vehicle_group_year)
                .ThenBy(vg => vg.vehicle_group_name)
                .ToList();

            // Return the filtered list of vehicle groups
            return Ok(filteredVehicleGroups);
        }
    }
}
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using stencil_residual_app_api.Data;
using stencil_residual_app_api.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stencil_residual_app_api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FinanceCompaniesController : ControllerBase
    {
        private readonly ResidualAppDbContext _context;

        public FinanceCompaniesController(ResidualAppDbContext context)
        {
            _context = context;
        }

        // GET: api/FinanceCompanies
        [HttpGet]
        public async Task<ActionResult<IEnumerable<FinanceCompany>>> GetFinanceCompanies()
        {
            return await _context.FinanceCompanies
                //.Where(fc => fc.tblstatus_StatusID == 3) // Active status
                .OrderBy(fc => fc.FiCoName)
                .ToListAsync();
        }

        // GET: api/FinanceCompanies/all
        [HttpGet("all")]
        public async Task<ActionResult<IEnumerable<FinanceCompany>>> GetAllFinanceCompanies()
        {
            return await _context.FinanceCompanies
                .OrderBy(fc => fc.FiCoName)
                .ToListAsync();
        }

        // GET: api/FinanceCompanies/5
        [HttpGet("{id}")]
        public async Task<ActionResult<FinanceCompany>> GetFinanceCompany(int id)
        {
            var financeCompany = await _context.FinanceCompanies.FindAsync(id);

            if (financeCompany == null)
            {
                return NotFound();
            }

            return financeCompany;
        }

        // GET: api/FinanceCompanies/dataSource/ALG
        [HttpGet("dataSource/{dataSource}")]
        public async Task<ActionResult<IEnumerable<ResidualFinanceCompany>>> GetFinanceCompaniesByDataSource(string dataSource)
        {
            var financeCompanies = (from residualMaster in _context.ResidualMasters
                                    join financeCompany in _context.FinanceCompanies
                                    on residualMaster.FiCoID equals financeCompany.FiCoID
                                    where residualMaster.data_source == dataSource
                                          && (residualMaster.tblstatus_StatusID == 3 || residualMaster.tblstatus_StatusID == 6)
                                    select new ResidualFinanceCompany
                                    {
                                        FiCoID = financeCompany.FiCoID,
                                        FiCoName = financeCompany.FiCoName,
                                        ModelYear = residualMaster.model_year,
                                        tblstatus_StatusID = residualMaster.tblstatus_StatusID,
                                        NewUsed = residualMaster.new_used
                                    }).OrderBy(f => f.FiCoID).ThenBy(f=>f.NewUsed).ThenBy(f=>f.ModelYear)
                       .ToList().DistinctBy(f=>f.FiCoID).ToList();
            var financialCompanyIds = financeCompanies.Select(c => c.FiCoID).ToList();

            var filteredMasters = _context.ResidualMasters
    .Where(c => c.data_source == dataSource)
    .AsEnumerable() // Forces client-side evaluation
    .Where(c => c.FiCoID != null && financialCompanyIds.Contains(c.FiCoID.Value))
    .ToList();

            foreach (var comp in financeCompanies)
            {
                var isStatusPending = filteredMasters.Any(c => c.FiCoID == comp.FiCoID && c.data_source == dataSource && c.tblstatus_StatusID == 6);
                if (isStatusPending)
                {
                    comp.tblstatus_StatusID = 6;
                }
            }
            return financeCompanies;
        }

        // GET: api/FinanceCompanies/activeWithResiduals
        [HttpGet("activeWithResiduals")]
        public async Task<ActionResult<IEnumerable<FinanceCompany>>> GetActiveFinanceCompaniesWithResiduals()
        {
            var today = System.DateTime.Today;
            
            // Get finance companies that have active residual masters
            var fiCoIds = await _context.ResidualMasters
                .Where(rm => rm.tblstatus_StatusID == 3 && // Active status
                            rm.startdate <= today && 
                            rm.stopdate >= today &&
                            rm.FiCoID.HasValue)
                .Select(rm => rm.FiCoID!.Value)
                .Distinct()
                .ToListAsync();

            var financeCompanies = await _context.FinanceCompanies
                //.Where(fc => fiCoIds.Contains(fc.FiCoID) && fc.tblstatus_StatusID == 3) // Active status
                .OrderBy(fc => fc.FiCoName)
                .ToListAsync();

            return financeCompanies;
        }

        // PUT: api/FinanceCompanies/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutFinanceCompany(int id, FinanceCompany financeCompany)
        {
            if (id != financeCompany.FiCoID)
            {
                return BadRequest();
            }

            _context.Entry(financeCompany).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!FinanceCompanyExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/FinanceCompanies
        [HttpPost]
        public async Task<ActionResult<FinanceCompany>> PostFinanceCompany(FinanceCompany financeCompany)
        {
            _context.FinanceCompanies.Add(financeCompany);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetFinanceCompany", new { id = financeCompany.FiCoID }, financeCompany);
        }

        // DELETE: api/FinanceCompanies/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFinanceCompany(int id)
        {
            var financeCompany = await _context.FinanceCompanies.FindAsync(id);
            if (financeCompany == null)
            {
                return NotFound();
            }

            // Check if there are any associated residual masters
            var hasAssociatedMasters = await _context.ResidualMasters
                .AnyAsync(rm => rm.FiCoID == id);

            if (hasAssociatedMasters)
            {
                return BadRequest("Cannot delete finance company because it is associated with one or more residual masters.");
            }

            _context.FinanceCompanies.Remove(financeCompany);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool FinanceCompanyExists(int id)
        {
            return _context.FinanceCompanies.Any(e => e.FiCoID == id);
        }
    }
}
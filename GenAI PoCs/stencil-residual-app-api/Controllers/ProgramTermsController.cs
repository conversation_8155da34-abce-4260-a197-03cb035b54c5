using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using stencil_residual_app_api.Data;
using stencil_residual_app_api.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stencil_residual_app_api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProgramTermsController : ControllerBase
    {
        private readonly ResidualAppDbContext _context;

        public ProgramTermsController(ResidualAppDbContext context)
        {
            _context = context;
        }

        // GET: api/ProgramTerms
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProgramTerm>>> GetProgramTerms()
        {
            return await _context.ProgramTerms
                .OrderBy(pt => pt.PgmTermMin)
                .ToListAsync();
        }

        // GET: api/ProgramTerms/5
        [HttpGet("{id}")]
        public async Task<ActionResult<ProgramTerm>> GetProgramTerm(int id)
        {
            var programTerm = await _context.ProgramTerms.FindAsync(id);

            if (programTerm == null)
            {
                return NotFound();
            }

            return programTerm;
        }

        // GET: api/ProgramTerms/forResidualMaster/1
        [HttpGet("forResidualMaster/{residualMasterId}")]
        public async Task<ActionResult<IEnumerable<ProgramTerm>>> GetProgramTermsForResidualMaster(int residualMasterId)
        {
            // Get program terms that are associated with the specified residual master
            var programTermIds = await _context.ResidualMasterTermTemplates
                .Where(rmtt => rmtt.ResidualMasterID == residualMasterId)
                .Select(rmtt => rmtt.PgmTermID)
                .ToListAsync();

            var programTerms = await _context.ProgramTerms
                .Where(pt => programTermIds.Contains(pt.PgmTermID))
                .OrderBy(pt => pt.PgmTermMin)
                .ToListAsync();

            return programTerms;
        }

        // PUT: api/ProgramTerms/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutProgramTerm(int id, ProgramTerm programTerm)
        {
            if (id != programTerm.PgmTermID)
            {
                return BadRequest();
            }

            _context.Entry(programTerm).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ProgramTermExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/ProgramTerms
        [HttpPost]
        public async Task<ActionResult<ProgramTerm>> PostProgramTerm(ProgramTerm programTerm)
        {
            _context.ProgramTerms.Add(programTerm);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetProgramTerm", new { id = programTerm.PgmTermID }, programTerm);
        }

        // DELETE: api/ProgramTerms/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProgramTerm(int id)
        {
            var programTerm = await _context.ProgramTerms.FindAsync(id);
            if (programTerm == null)
            {
                return NotFound();
            }

            // Check if there are any associated residual master term templates
            var hasAssociatedTemplates = await _context.ResidualMasterTermTemplates
                .AnyAsync(rmtt => rmtt.PgmTermID == id);

            if (hasAssociatedTemplates)
            {
                return BadRequest("Cannot delete program term because it is associated with one or more residual master term templates.");
            }

            // Check if there are any associated residual values
            var hasAssociatedValues = await _context.ResidualValues
                .AnyAsync(rv => rv.PgmTermID == id);

            if (hasAssociatedValues)
            {
                return BadRequest("Cannot delete program term because it is associated with one or more residual values.");
            }

            _context.ProgramTerms.Remove(programTerm);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool ProgramTermExists(int id)
        {
            return _context.ProgramTerms.Any(e => e.PgmTermID == id);
        }
    }
}
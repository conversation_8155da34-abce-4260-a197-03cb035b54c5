using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using stencil_residual_app_api.Data;
using stencil_residual_app_api.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace stencil_residual_app_api.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DivisionsController : ControllerBase
    {
        private readonly ResidualAppDbContext _context;

        public DivisionsController(ResidualAppDbContext context)
        {
            _context = context;
        }

        // GET: api/Divisions
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Division>>> GetDivisions()
        {
            return await _context.Divisions
                .OrderBy(d => d.ManufactName)
                .ThenBy(d => d.DivName)
                .ToListAsync();
        }

        // GET: api/Divisions/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Division>> GetDivision(int id)
        {
            var division = await _context.Divisions.FindAsync(id);

            if (division == null)
            {
                return NotFound();
            }

            return division;
        }

        // GET: api/Divisions/byManufacturer/Toyota
        [HttpGet("byManufacturer/{manufactName}")]
        public async Task<ActionResult<IEnumerable<Division>>> GetDivisionsByManufacturer(string manufactName)
        {
            return await _context.Divisions
                .Where(d => d.ManufactName.Contains(manufactName))
                .OrderBy(d => d.DivName)
                .ToListAsync();
        }

        // GET: api/Divisions/forActiveResiduals
        [HttpGet("forActiveResiduals")]
        public async Task<ActionResult<IEnumerable<Division>>> GetDivisionsForActiveResiduals()
        {
            var today = DateTime.Today;
            
            // Get divisions with active residual masters
            var divisionIds = await _context.ResidualDivisions
                .Include(rd => rd.ResidualMaster)
                .Where(rd => rd.ResidualMaster.tblstatus_StatusID == 3 && // Active status
                           rd.ResidualMaster.startdate <= today && 
                           rd.ResidualMaster.stopdate >= today)
                .Select(rd => rd.DivisionID)
                .Distinct()
                .ToListAsync();

            var divisions = await _context.Divisions
                .Where(d => divisionIds.Contains(d.DivisionID))
                .OrderBy(d => d.ManufactName)
                .ThenBy(d => d.DivName)
                .ToListAsync();

            return divisions;
        }

        // GET: api/Divisions/forDataSource/ALG
        [HttpGet("forDataSource/{dataSource}")]
        public async Task<ActionResult<IEnumerable<Division>>> GetDivisionsForDataSource(string dataSource)
        {
            var today = DateTime.Today;
            
            // Get divisions with active residual masters for the specified data source
            var divisionIds = await _context.ResidualDivisions
                .Include(rd => rd.ResidualMaster)
                .Where(rd => rd.ResidualMaster.tblstatus_StatusID == 3 && // Active status
                           rd.ResidualMaster.startdate <= today && 
                           rd.ResidualMaster.stopdate >= today &&
                           rd.ResidualMaster.data_source == dataSource)
                .Select(rd => rd.DivisionID)
                .Distinct()
                .ToListAsync();

            var divisions = await _context.Divisions
                .Where(d => divisionIds.Contains(d.DivisionID))
                .OrderBy(d => d.ManufactName)
                .ThenBy(d => d.DivName)
                .ToListAsync();

            return divisions;
        }

        // GET: api/Divisions/forFinanceCompany/1
        [HttpGet("forFinanceCompany/{ficoId}")]
        public async Task<ActionResult<IEnumerable<Division>>> GetDivisionsForFinanceCompany(int ficoId)
        {
            var today = DateTime.Today;
            
            // Get divisions with active residual masters for the specified finance company
            var divisionIds = await _context.ResidualDivisions
                .Include(rd => rd.ResidualMaster)
                .Where(rd => rd.ResidualMaster.tblstatus_StatusID == 3 && // Active status
                           rd.ResidualMaster.startdate <= today && 
                           rd.ResidualMaster.stopdate >= today &&
                           rd.ResidualMaster.FiCoID == ficoId)
                .Select(rd => rd.DivisionID)
                .Distinct()
                .ToListAsync();

            var divisions = await _context.Divisions
                .Where(d => divisionIds.Contains(d.DivisionID))
                .OrderBy(d => d.ManufactName)
                .ThenBy(d => d.DivName)
                .ToListAsync();

            return divisions;
        }

        // GET: api/Divisions/forDataSourceAndFinanceCompany/ALG/1
        [HttpGet("forDataSourceAndFinanceCompany/{dataSource}/{ficoId}")]
        public async Task<ActionResult<IEnumerable<Division>>> GetDivisionsForDataSourceAndFinanceCompany(string dataSource, int ficoId)
        {
            var today = DateTime.Today;
            
            // Get divisions with active residual masters for the specified data source and finance company
            var divisionIds = await _context.ResidualDivisions
                .Include(rd => rd.ResidualMaster)
                .Where(rd => rd.ResidualMaster.tblstatus_StatusID == 3 && // Active status
                           rd.ResidualMaster.startdate <= today && 
                           rd.ResidualMaster.stopdate >= today &&
                           rd.ResidualMaster.data_source == dataSource &&
                           rd.ResidualMaster.FiCoID == ficoId)
                .Select(rd => rd.DivisionID)
                .Distinct()
                .ToListAsync();

            var divisions = await _context.Divisions
                .Where(d => divisionIds.Contains(d.DivisionID))
                .OrderBy(d => d.ManufactName)
                .ThenBy(d => d.DivName)
                .ToListAsync();

            return divisions;
        }

        // PUT: api/Divisions/5
        [HttpPut("{id}")]
        public async Task<IActionResult> PutDivision(int id, Division division)
        {
            if (id != division.DivisionID)
            {
                return BadRequest();
            }

            _context.Entry(division).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!DivisionExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Divisions
        [HttpPost]
        public async Task<ActionResult<Division>> PostDivision(Division division)
        {
            _context.Divisions.Add(division);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetDivision", new { id = division.DivisionID }, division);
        }

        // DELETE: api/Divisions/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDivision(int id)
        {
            var division = await _context.Divisions.FindAsync(id);
            if (division == null)
            {
                return NotFound();
            }

            // Check if there are any associated residual masters
            var hasAssociatedMasters = await _context.ResidualDivisions
                .AnyAsync(rd => rd.DivisionID == id);

            if (hasAssociatedMasters)
            {
                return BadRequest("Cannot delete division because it is associated with one or more residual masters.");
            }

            _context.Divisions.Remove(division);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool DivisionExists(int id)
        {
            return _context.Divisions.Any(e => e.DivisionID == id);
        }
    }
}
# Development Environment Configuration

# AWS Configuration
aws_region = "us-east-1"

# EKS Configuration
kubernetes_version = "1.28"
vpc_cidr          = "10.0.0.0/16"
private_subnets   = ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
public_subnets    = ["10.0.101.0/24", "10.0.102.0/24", "10.0.103.0/24"]

# Node Group Configuration (Cost-optimized for dev)
node_instance_types     = ["t3.medium", "t3a.medium"]
node_capacity_type      = "SPOT"
node_group_min_size     = 1
node_group_max_size     = 3
node_group_desired_size = 2
node_disk_size         = 30

# Security Configuration (More permissive for dev)
allowed_cidr_blocks = ["0.0.0.0/0"]

# Logging Configuration (Minimal for dev)
log_retention_days = 3

# RDS Configuration (Minimal for dev)
mysql_version           = "8.0.35"
rds_instance_class      = "db.t3.micro"
rds_allocated_storage   = 20
rds_max_allocated_storage = 50

# Database Configuration
database_name     = "aisdb3_digirs"
database_username = "aisadmin"
# database_password will be auto-generated

# Backup Configuration (Minimal for dev)
backup_retention_period = 3
backup_window          = "03:00-04:00"
maintenance_window     = "sun:04:00-sun:05:00"

# Monitoring Configuration (Disabled for dev)
monitoring_interval                   = 0
enabled_cloudwatch_logs_exports       = ["error"]
cloudwatch_log_group_retention_in_days = 3

# Lambda Configuration
lambda_schedule_expression = "rate(1 hour)"

# Email Configuration
smtp_server = "smtp-sdc.coxautoinc.com"
smtp_user   = "smtp"
# smtp_password should be set via environment variable or secrets

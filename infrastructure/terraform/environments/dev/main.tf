# Development Environment Infrastructure
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.20"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.10"
    }
  }

  backend "s3" {
    bucket         = "ais-modernization-terraform-state-dev"
    key            = "dev/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "ais-modernization-terraform-locks"
  }
}

# Configure providers
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment   = "dev"
      Project       = "ais-modernization"
      ManagedBy     = "terraform"
      Owner         = "devops-team"
      CostCenter    = "engineering"
    }
  }
}

# Data sources
data "aws_eks_cluster" "cluster" {
  name = module.eks.cluster_name
  depends_on = [module.eks]
}

data "aws_eks_cluster_auth" "cluster" {
  name = module.eks.cluster_name
  depends_on = [module.eks]
}

# Configure Kubernetes provider
provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

# Configure Helm provider
provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}

# Local values
locals {
  environment = "dev"
  project     = "ais-modernization"
  
  common_tags = {
    Environment = local.environment
    Project     = local.project
    ManagedBy   = "terraform"
  }
}

# EKS Cluster
module "eks" {
  source = "../../modules/eks"

  cluster_name       = "${local.project}-${local.environment}"
  environment        = local.environment
  kubernetes_version = var.kubernetes_version

  vpc_cidr        = var.vpc_cidr
  private_subnets = var.private_subnets
  public_subnets  = var.public_subnets

  node_instance_types      = var.node_instance_types
  node_capacity_type       = var.node_capacity_type
  node_group_min_size      = var.node_group_min_size
  node_group_max_size      = var.node_group_max_size
  node_group_desired_size  = var.node_group_desired_size
  node_disk_size          = var.node_disk_size

  allowed_cidr_blocks = var.allowed_cidr_blocks
  cluster_addons      = var.cluster_addons
  log_retention_days  = var.log_retention_days

  tags = local.common_tags
}

# RDS MySQL Database
module "rds" {
  source = "../../modules/rds"

  identifier     = "${local.project}-${local.environment}"
  engine         = "mysql"
  engine_version = var.mysql_version
  instance_class = var.rds_instance_class

  allocated_storage     = var.rds_allocated_storage
  max_allocated_storage = var.rds_max_allocated_storage
  storage_encrypted     = true

  db_name  = var.database_name
  username = var.database_username
  password = var.database_password

  vpc_id     = module.eks.vpc_id
  subnet_ids = module.eks.private_subnets

  allowed_security_groups = [module.eks.node_security_group_id]
  allowed_cidr_blocks     = [var.vpc_cidr]

  backup_retention_period = var.backup_retention_period
  backup_window          = var.backup_window
  maintenance_window     = var.maintenance_window

  monitoring_interval                   = var.monitoring_interval
  enabled_cloudwatch_logs_exports       = var.enabled_cloudwatch_logs_exports
  cloudwatch_log_group_retention_in_days = var.cloudwatch_log_group_retention_in_days

  deletion_protection = false # Set to false for dev environment
  skip_final_snapshot = true  # Set to true for dev environment

  create_secrets_manager_secret = true

  tags = local.common_tags
}

# Frontend S3 + CloudFront
module "frontend" {
  source = "../../modules/frontend"

  bucket_name         = "${local.project}-frontend-${local.environment}"
  default_root_object = "index.html"

  # Custom error responses for SPA
  custom_error_responses = [
    {
      error_code         = 404
      response_code      = 200
      response_page_path = "/index.html"
    },
    {
      error_code         = 403
      response_code      = 200
      response_page_path = "/index.html"
    }
  ]

  price_class = "PriceClass_100" # Use only North America and Europe for dev

  # Security settings
  content_security_policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.amazonaws.com"

  enable_monitoring = true
  alarm_actions     = [] # Add SNS topic ARN for notifications

  tags = local.common_tags
}

# Lambda function for vehicle export worker
resource "aws_lambda_function" "vehicle_export_worker" {
  function_name = "${local.project}-vehicle-export-worker-${local.environment}"
  role         = aws_iam_role.lambda_execution_role.arn
  handler      = "DescribedVehicleExportWorker::DescribedVehicleExportWorker.Function::FunctionHandler"
  runtime      = "dotnet8"
  timeout      = 300
  memory_size  = 512

  filename         = "placeholder.zip" # This will be updated by CI/CD
  source_code_hash = "placeholder"     # This will be updated by CI/CD

  environment {
    variables = {
      ENVIRONMENT                  = local.environment
      DATABASE_CONNECTION_STRING   = module.rds.connection_string
      SMTP_SERVER                 = var.smtp_server
      SMTP_USER                   = var.smtp_user
      SMTP_PASSWORD               = var.smtp_password
      ACTIVE_DESC_VEH_SAVE_DIRECTORY = "/tmp"
    }
  }

  vpc_config {
    subnet_ids         = module.eks.private_subnets
    security_group_ids = [aws_security_group.lambda.id]
  }

  tags = local.common_tags
}

# IAM role for Lambda
resource "aws_iam_role" "lambda_execution_role" {
  name = "${local.project}-lambda-execution-role-${local.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = local.common_tags
}

# IAM policy attachments for Lambda
resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
  role       = aws_iam_role.lambda_execution_role.name
}

resource "aws_iam_role_policy_attachment" "lambda_vpc_access" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
  role       = aws_iam_role.lambda_execution_role.name
}

# Security group for Lambda
resource "aws_security_group" "lambda" {
  name_prefix = "${local.project}-lambda-${local.environment}-"
  vpc_id      = module.eks.vpc_id
  description = "Security group for Lambda functions"

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  tags = merge(local.common_tags, {
    Name = "${local.project}-lambda-${local.environment}"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# EventBridge rule for scheduled Lambda execution
resource "aws_cloudwatch_event_rule" "vehicle_export_schedule" {
  name                = "${local.project}-vehicle-export-schedule-${local.environment}"
  description         = "Trigger vehicle export worker Lambda"
  schedule_expression = var.lambda_schedule_expression

  tags = local.common_tags
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.vehicle_export_schedule.name
  target_id = "VehicleExportWorkerTarget"
  arn       = aws_lambda_function.vehicle_export_worker.arn
}

resource "aws_lambda_permission" "allow_eventbridge" {
  statement_id  = "AllowExecutionFromEventBridge"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.vehicle_export_worker.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.vehicle_export_schedule.arn
}

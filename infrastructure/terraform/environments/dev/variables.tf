# Development Environment Variables

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

# EKS Configuration
variable "kubernetes_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "private_subnets" {
  description = "Private subnet CIDR blocks"
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
}

variable "public_subnets" {
  description = "Public subnet CIDR blocks"
  type        = list(string)
  default     = ["**********/24", "**********/24", "**********/24"]
}

variable "node_instance_types" {
  description = "Instance types for EKS node group"
  type        = list(string)
  default     = ["t3.medium"]
}

variable "node_capacity_type" {
  description = "Capacity type for node group (ON_DEMAND or SPOT)"
  type        = string
  default     = "SPOT" # Use SPOT instances for dev to save costs
}

variable "node_group_min_size" {
  description = "Minimum number of nodes in the node group"
  type        = number
  default     = 1
}

variable "node_group_max_size" {
  description = "Maximum number of nodes in the node group"
  type        = number
  default     = 3
}

variable "node_group_desired_size" {
  description = "Desired number of nodes in the node group"
  type        = number
  default     = 2
}

variable "node_disk_size" {
  description = "Disk size for worker nodes in GB"
  type        = number
  default     = 30
}

variable "allowed_cidr_blocks" {
  description = "List of CIDR blocks allowed to access the cluster"
  type        = list(string)
  default     = ["0.0.0.0/0"] # Open for dev, restrict in production
}

variable "cluster_addons" {
  description = "Map of cluster addon configurations"
  type = map(object({
    version                   = string
    service_account_role_arn = optional(string)
  }))
  default = {
    coredns = {
      version = "v1.10.1-eksbuild.5"
    }
    kube-proxy = {
      version = "v1.28.2-eksbuild.2"
    }
    vpc-cni = {
      version = "v1.15.1-eksbuild.1"
    }
    aws-ebs-csi-driver = {
      version = "v1.24.0-eksbuild.1"
    }
  }
}

variable "log_retention_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 3 # Short retention for dev
}

# RDS Configuration
variable "mysql_version" {
  description = "MySQL version"
  type        = string
  default     = "8.0.35"
}

variable "rds_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t3.micro" # Small instance for dev
}

variable "rds_allocated_storage" {
  description = "RDS allocated storage in GB"
  type        = number
  default     = 20
}

variable "rds_max_allocated_storage" {
  description = "RDS maximum allocated storage in GB"
  type        = number
  default     = 50
}

variable "database_name" {
  description = "Name of the database to create"
  type        = string
  default     = "aisdb3_digirs"
}

variable "database_username" {
  description = "Username for the master DB user"
  type        = string
  default     = "aisadmin"
}

variable "database_password" {
  description = "Password for the master DB user"
  type        = string
  sensitive   = true
  default     = null # Will be auto-generated if not provided
}

variable "backup_retention_period" {
  description = "The days to retain backups for"
  type        = number
  default     = 3 # Short retention for dev
}

variable "backup_window" {
  description = "The daily time range during which automated backups are created"
  type        = string
  default     = "03:00-04:00"
}

variable "maintenance_window" {
  description = "The window to perform maintenance in"
  type        = string
  default     = "sun:04:00-sun:05:00"
}

variable "monitoring_interval" {
  description = "The interval for collecting enhanced monitoring metrics"
  type        = number
  default     = 0 # Disabled for dev to save costs
}

variable "enabled_cloudwatch_logs_exports" {
  description = "List of log types to export to CloudWatch"
  type        = list(string)
  default     = ["error"] # Only error logs for dev
}

variable "cloudwatch_log_group_retention_in_days" {
  description = "The number of days to retain CloudWatch logs"
  type        = number
  default     = 3
}

# Lambda Configuration
variable "lambda_schedule_expression" {
  description = "Schedule expression for Lambda function"
  type        = string
  default     = "rate(1 hour)" # Run every hour in dev
}

# Email Configuration
variable "smtp_server" {
  description = "SMTP server for email notifications"
  type        = string
  default     = "smtp-sdc.coxautoinc.com"
}

variable "smtp_user" {
  description = "SMTP username"
  type        = string
  default     = "smtp"
}

variable "smtp_password" {
  description = "SMTP password"
  type        = string
  sensitive   = true
  default     = ""
}

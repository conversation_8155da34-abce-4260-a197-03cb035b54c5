# Default values for ais-api
# This is a YAML-formatted file.

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# Common labels
commonLabels: {}
commonAnnotations: {}

# Image configuration
image:
  registry: ghcr.io
  repository: your-org/ais-modernization/api
  tag: ""
  digest: ""
  pullPolicy: IfNotPresent
  pullSecrets: []

# Deployment configuration
replicaCount: 2

# Update strategy
updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1
    maxSurge: 1

# Pod configuration
podLabels: {}
podAnnotations: {}
podSecurityContext:
  fsGroup: 2000
  runAsNonRoot: true
  runAsUser: 1000

# Container configuration
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

# Service configuration
service:
  type: ClusterIP
  port: 80
  targetPort: 8080
  annotations: {}
  labels: {}

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: api-dev.yourcompany.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: api-tls
      hosts:
        - api-dev.yourcompany.com

# Resource limits and requests
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

# Horizontal Pod Autoscaler
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# Vertical Pod Autoscaler
verticalPodAutoscaler:
  enabled: false
  updateMode: "Off"

# Pod Disruption Budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Node selection
nodeSelector: {}
tolerations: []
affinity: {}

# Topology spread constraints
topologySpreadConstraints: []

# Priority class
priorityClassName: ""

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Environment variables
env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "Development"
  - name: ASPNETCORE_URLS
    value: "http://+:8080"

# Environment variables from secrets/configmaps
envFrom: []

# Application configuration
config:
  # Database configuration
  database:
    connectionString: ""
    # Connection string will be injected via secret

  # Logging configuration
  logging:
    logLevel:
      default: "Information"
      microsoft: "Warning"
      microsoftHostingLifetime: "Information"

  # CORS configuration
  cors:
    allowedOrigins:
      - "http://localhost:3333"
      - "https://frontend-dev.yourcompany.com"

# Secrets configuration
secrets:
  database:
    connectionString: ""
  # Additional secrets can be added here

# ConfigMap configuration
configMap:
  data: {}

# Health checks
healthCheck:
  enabled: true
  livenessProbe:
    httpGet:
      path: /health
      port: http
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
    successThreshold: 1
  readinessProbe:
    httpGet:
      path: /health/ready
      port: http
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
    successThreshold: 1
  startupProbe:
    httpGet:
      path: /health
      port: http
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 30
    successThreshold: 1

# Volume mounts
volumeMounts:
  - name: tmp
    mountPath: /tmp
  - name: var-log
    mountPath: /var/log

# Volumes
volumes:
  - name: tmp
    emptyDir: {}
  - name: var-log
    emptyDir: {}

# Persistence
persistence:
  enabled: false
  storageClass: ""
  accessModes:
    - ReadWriteOnce
  size: 8Gi
  annotations: {}

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: ""
    labels: {}
    annotations: {}
    interval: 30s
    scrapeTimeout: 10s
    path: /metrics
    port: http

# Network policies
networkPolicy:
  enabled: false
  ingress: []
  egress: []

# Pod security policy
podSecurityPolicy:
  enabled: false

# RBAC
rbac:
  create: true
  rules: []

# Init containers
initContainers: []

# Sidecar containers
sidecars: []

# Extra objects
extraObjects: []

# Multi-stage Dockerfile for ASP.NET Core API
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy csproj and restore dependencies
COPY ["stencil-residual-app-api.csproj", "./"]
RUN dotnet restore "stencil-residual-app-api.csproj"

# Copy source code and build
COPY . .
RUN dotnet build "stencil-residual-app-api.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "stencil-residual-app-api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Install security updates and clean up
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        ca-certificates \
        curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy published app
COPY --from=publish /app/publish .

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /tmp/app && \
    chown -R appuser:appuser /app /tmp/app && \
    chmod -R 755 /app && \
    chmod -R 777 /tmp/app

# Switch to non-root user
USER appuser

# Configure ASP.NET Core
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port
EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["dotnet", "stencil-residual-app-api.dll"]

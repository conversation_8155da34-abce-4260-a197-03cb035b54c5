# Multi-stage Dockerfile for .NET Worker Service
# Build stage
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy solution file
COPY ["VehicleExportWorkerService.sln", "./"]

# Copy project files
COPY ["VehicleExportWorkerService/VehicleExportWorkerService.csproj", "VehicleExportWorkerService/"]
COPY ["VehicleExportWorkerService.Tests/VehicleExportWorkerService.Tests.csproj", "VehicleExportWorkerService.Tests/"]

# Restore dependencies
RUN dotnet restore "VehicleExportWorkerService.sln"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/VehicleExportWorkerService"
RUN dotnet build "VehicleExportWorkerService.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "VehicleExportWorkerService.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/runtime:8.0 AS runtime

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Install security updates and required packages
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
        ca-certificates \
        curl \
        procps && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy published app
COPY --from=publish /app/publish .

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /app/data /tmp/app && \
    chown -R appuser:appuser /app /tmp/app && \
    chmod -R 755 /app && \
    chmod -R 777 /tmp/app /app/data

# Switch to non-root user
USER appuser

# Configure .NET
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Health check (simple process check since this is a worker service)
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD pgrep -f "VehicleExportWorkerService" || exit 1

# Set entrypoint
ENTRYPOINT ["dotnet", "VehicleExportWorkerService.dll"]

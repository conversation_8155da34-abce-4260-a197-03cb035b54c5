# Troubleshooting Guide

This guide provides solutions to common issues encountered with the AIS Modernization CI/CD pipeline and infrastructure.

## Table of Contents

1. [CI/CD Pipeline Issues](#cicd-pipeline-issues)
2. [Infrastructure Issues](#infrastructure-issues)
3. [Application Issues](#application-issues)
4. [Security Issues](#security-issues)
5. [Performance Issues](#performance-issues)
6. [Monitoring and Debugging](#monitoring-and-debugging)

## CI/CD Pipeline Issues

### 1. GitHub Actions Workflow Failures

#### Build Failures

**Symptom**: Build jobs failing with compilation errors
```
Error: The build failed with exit code 1
```

**Solution**:
```bash
# Check the specific error in the workflow logs
gh run list --workflow=ci-cd-main.yml
gh run view <run-id> --job=<job-id>

# Common fixes:
# 1. Update dependencies
cd "GenAI PoCs/stencil-residual-app"
npm update

# 2. Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# 3. For .NET projects
cd "GenAI PoCs/stencil-residual-app-api"
dotnet clean
dotnet restore
dotnet build
```

#### Test Failures

**Symptom**: Tests failing in CI but passing locally
```
Error: Test suite failed to run
```

**Solution**:
```bash
# Run tests locally with same environment
export CI=true
export NODE_ENV=test

# For StencilJS tests
cd "GenAI PoCs/stencil-residual-app"
npm test -- --ci --coverage --watchAll=false

# For .NET tests
cd "GenAI PoCs/stencil-residual-app-api"
dotnet test --configuration Release --logger trx --collect:"XPlat Code Coverage"
```

#### Docker Build Failures

**Symptom**: Container builds failing
```
Error: failed to solve: process "/bin/sh -c dotnet restore" did not complete successfully
```

**Solution**:
```bash
# Test Docker build locally
docker build -f infrastructure/docker/api/Dockerfile "GenAI PoCs/stencil-residual-app-api" -t test-api

# Common issues:
# 1. Check Dockerfile syntax
# 2. Verify base image availability
# 3. Check network connectivity in build environment

# Debug with intermediate steps
docker build --no-cache --progress=plain -f infrastructure/docker/api/Dockerfile "GenAI PoCs/stencil-residual-app-api"
```

### 2. Deployment Failures

#### Terraform Deployment Issues

**Symptom**: Terraform apply failing
```
Error: Error creating EKS Cluster: InvalidParameterException
```

**Solution**:
```bash
# Check Terraform state
cd infrastructure/terraform/environments/dev
terraform state list
terraform plan -detailed-exitcode

# Common fixes:
# 1. Check AWS credentials and permissions
aws sts get-caller-identity
aws iam get-user

# 2. Verify resource limits
aws service-quotas get-service-quota --service-code eks --quota-code L-1194D53C

# 3. Check for resource conflicts
terraform import aws_eks_cluster.main existing-cluster-name
```

#### Kubernetes Deployment Issues

**Symptom**: Pods not starting or crashing
```
Error: CrashLoopBackOff
```

**Solution**:
```bash
# Check pod status
kubectl get pods -n ais-dev
kubectl describe pod <pod-name> -n ais-dev
kubectl logs <pod-name> -n ais-dev --previous

# Common fixes:
# 1. Check resource limits
kubectl top pods -n ais-dev

# 2. Verify secrets and configmaps
kubectl get secrets -n ais-dev
kubectl get configmaps -n ais-dev

# 3. Check image pull issues
kubectl describe pod <pod-name> -n ais-dev | grep -A 10 "Events:"
```

## Infrastructure Issues

### 1. EKS Cluster Issues

#### Node Group Not Ready

**Symptom**: Worker nodes not joining the cluster
```
Error: Node group is not ready
```

**Solution**:
```bash
# Check node group status
aws eks describe-nodegroup --cluster-name ais-modernization-dev --nodegroup-name main

# Check node status
kubectl get nodes
kubectl describe node <node-name>

# Common fixes:
# 1. Check IAM roles and policies
aws iam get-role --role-name <node-group-role>

# 2. Verify security groups
aws ec2 describe-security-groups --group-ids <security-group-id>

# 3. Check subnet configuration
aws ec2 describe-subnets --subnet-ids <subnet-id>
```

#### Cluster Access Issues

**Symptom**: Unable to connect to EKS cluster
```
Error: You must be logged in to the server (Unauthorized)
```

**Solution**:
```bash
# Update kubeconfig
aws eks update-kubeconfig --region us-east-1 --name ais-modernization-dev

# Check AWS credentials
aws sts get-caller-identity

# Verify cluster access
kubectl auth can-i get pods --all-namespaces

# Add user to cluster access
aws eks create-access-entry --cluster-name ais-modernization-dev --principal-arn arn:aws:iam::123456789012:user/username
```

### 2. RDS Database Issues

#### Connection Failures

**Symptom**: Applications cannot connect to database
```
Error: Unable to connect to any of the specified MySQL hosts
```

**Solution**:
```bash
# Check RDS instance status
aws rds describe-db-instances --db-instance-identifier ais-modernization-dev

# Test connectivity from EKS
kubectl run mysql-test --image=mysql:8.0 --rm -it --restart=Never -- mysql -h <rds-endpoint> -u <username> -p

# Common fixes:
# 1. Check security groups
aws ec2 describe-security-groups --group-ids <rds-security-group-id>

# 2. Verify subnet groups
aws rds describe-db-subnet-groups --db-subnet-group-name <subnet-group-name>

# 3. Check connection string format
# Correct format: server=endpoint;database=dbname;uid=username;pwd=password;port=3306
```

#### Performance Issues

**Symptom**: Slow database queries
```
Warning: Query execution time exceeded threshold
```

**Solution**:
```bash
# Check RDS performance insights
aws rds describe-db-instances --db-instance-identifier ais-modernization-dev --query 'DBInstances[0].PerformanceInsightsEnabled'

# Monitor slow queries
# Enable slow query log in RDS parameter group
aws rds describe-db-log-files --db-instance-identifier ais-modernization-dev

# Optimize queries and add indexes as needed
```

### 3. Load Balancer Issues

#### ALB Not Responding

**Symptom**: Application Load Balancer returning 503 errors
```
Error: 503 Service Temporarily Unavailable
```

**Solution**:
```bash
# Check ALB status
aws elbv2 describe-load-balancers --names <alb-name>

# Check target group health
aws elbv2 describe-target-health --target-group-arn <target-group-arn>

# Verify ingress controller
kubectl get pods -n kube-system | grep aws-load-balancer-controller
kubectl logs -n kube-system deployment/aws-load-balancer-controller

# Common fixes:
# 1. Check security groups
# 2. Verify health check configuration
# 3. Ensure pods are ready and healthy
```

## Application Issues

### 1. StencilJS Frontend Issues

#### Build Failures

**Symptom**: Frontend build failing
```
Error: Module not found: Error: Can't resolve './component'
```

**Solution**:
```bash
cd "GenAI PoCs/stencil-residual-app"

# Clear cache and reinstall
rm -rf node_modules .stencil dist
npm install

# Check for TypeScript errors
npm run build

# Verify component imports and exports
```

#### Runtime Errors

**Symptom**: Application not loading in browser
```
Error: Uncaught TypeError: Cannot read property 'x' of undefined
```

**Solution**:
```bash
# Check browser console for errors
# Verify API endpoints are accessible
curl -f https://api-dev.yourcompany.com/health

# Check CORS configuration in API
# Verify environment variables
```

### 2. .NET API Issues

#### Startup Failures

**Symptom**: API container crashing on startup
```
Error: Application startup exception
```

**Solution**:
```bash
# Check container logs
kubectl logs <api-pod-name> -n ais-dev

# Common issues:
# 1. Database connection string
# 2. Missing environment variables
# 3. Certificate issues

# Debug locally
cd "GenAI PoCs/stencil-residual-app-api"
dotnet run --environment Development
```

#### Database Migration Issues

**Symptom**: Entity Framework migrations failing
```
Error: A network-related or instance-specific error occurred
```

**Solution**:
```bash
# Run migrations manually
cd "GenAI PoCs/stencil-residual-app-api"
dotnet ef database update

# Check connection string
dotnet ef dbcontext info

# Verify database accessibility
mysql -h <rds-endpoint> -u <username> -p
```

### 3. Worker Service Issues

#### Processing Failures

**Symptom**: Background jobs not processing
```
Error: Worker service stopped unexpectedly
```

**Solution**:
```bash
# Check worker service logs
kubectl logs <worker-pod-name> -n ais-dev

# Verify database connectivity
# Check email configuration
# Monitor resource usage

# Debug locally
cd "GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src/VehicleExportWorkerService"
dotnet run
```

## Security Issues

### 1. Security Scan Failures

#### High Severity Vulnerabilities

**Symptom**: Security pipeline failing due to vulnerabilities
```
Error: High severity vulnerabilities found
```

**Solution**:
```bash
# Review security scan results
gh run view <run-id> --job=security-scan

# Update vulnerable dependencies
# For npm packages
npm audit fix

# For NuGet packages
dotnet list package --vulnerable
dotnet add package <package-name> --version <safe-version>

# Review and suppress false positives if necessary
```

#### Secret Detection

**Symptom**: Secrets detected in repository
```
Error: Potential secret found in commit
```

**Solution**:
```bash
# Remove secrets from history
git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch <file-with-secret>' --prune-empty --tag-name-filter cat -- --all

# Use proper secret management
# Store secrets in GitHub Secrets or AWS Secrets Manager
# Update applications to use secret management services
```

### 2. Access Control Issues

#### RBAC Failures

**Symptom**: Pods cannot access required resources
```
Error: Forbidden: User cannot get resource "secrets"
```

**Solution**:
```bash
# Check service account permissions
kubectl get serviceaccount <service-account> -n ais-dev -o yaml
kubectl describe clusterrolebinding <binding-name>

# Verify RBAC configuration
kubectl auth can-i get secrets --as=system:serviceaccount:ais-dev:<service-account>

# Update RBAC rules if necessary
```

## Performance Issues

### 1. Slow Application Response

#### High Latency

**Symptom**: API responses taking too long
```
Warning: Response time > 2000ms
```

**Solution**:
```bash
# Check application metrics
kubectl top pods -n ais-dev

# Monitor database performance
# Check for slow queries in RDS Performance Insights

# Optimize application code
# Add caching where appropriate
# Scale horizontally if needed

# Increase resource limits
kubectl edit deployment <deployment-name> -n ais-dev
```

### 2. Resource Exhaustion

#### Out of Memory

**Symptom**: Pods being killed due to memory limits
```
Error: OOMKilled
```

**Solution**:
```bash
# Check resource usage
kubectl top pods -n ais-dev
kubectl describe pod <pod-name> -n ais-dev

# Increase memory limits
# Optimize application memory usage
# Check for memory leaks

# Update resource requests and limits
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"
```

## Monitoring and Debugging

### 1. Logging

#### Centralized Logging

```bash
# Check application logs
kubectl logs <pod-name> -n ais-dev --follow

# Check system logs
kubectl get events -n ais-dev --sort-by=.metadata.creationTimestamp

# CloudWatch logs
aws logs describe-log-groups
aws logs get-log-events --log-group-name /aws/eks/ais-modernization-dev/cluster
```

### 2. Metrics and Monitoring

#### Prometheus Queries

```promql
# CPU usage
rate(container_cpu_usage_seconds_total[5m])

# Memory usage
container_memory_usage_bytes / container_spec_memory_limit_bytes

# HTTP request rate
rate(http_requests_total[5m])

# Error rate
rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
```

### 3. Health Checks

#### Application Health

```bash
# Check health endpoints
curl -f https://api-dev.yourcompany.com/health
curl -f https://api-dev.yourcompany.com/health/ready

# Kubernetes health checks
kubectl get pods -n ais-dev
kubectl describe pod <pod-name> -n ais-dev | grep -A 10 "Conditions:"
```

## Getting Help

### 1. Internal Support

- **DevOps Team**: #devops Slack channel
- **Security Team**: #security Slack channel
- **Development Team**: #development Slack channel

### 2. External Resources

- [AWS Support](https://aws.amazon.com/support/)
- [GitHub Support](https://support.github.com/)
- [Kubernetes Documentation](https://kubernetes.io/docs/tasks/debug-application-cluster/)
- [Terraform Documentation](https://www.terraform.io/docs/index.html)

### 3. Emergency Contacts

- **On-call Engineer**: +1-555-ONCALL
- **Security Incidents**: <EMAIL>
- **Infrastructure Issues**: <EMAIL>

## Escalation Procedures

1. **Level 1**: Team member attempts resolution using this guide
2. **Level 2**: Escalate to team lead or senior engineer
3. **Level 3**: Escalate to DevOps/Platform team
4. **Level 4**: Escalate to management and external support if needed

Remember to document any new issues and solutions to improve this troubleshooting guide.

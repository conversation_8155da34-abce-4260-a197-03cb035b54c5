# Architecture Documentation

This document provides a comprehensive overview of the AIS Modernization architecture, including CI/CD pipelines, infrastructure, and application components.

## System Architecture Overview

```mermaid
graph TB
    subgraph "Developer Workflow"
        DEV[Developer] --> GIT[Git Repository]
        GIT --> PR[Pull Request]
    end

    subgraph "CI/CD Pipeline"
        PR --> DETECT[Change Detection]
        DETECT --> BUILD[Build & Test]
        BUILD --> SECURITY[Security Scan]
        SECURITY --> CONTAINER[Container Build]
        CONTAINER --> DEPLOY[Deploy]
    end

    subgraph "AWS Infrastructure"
        DEPLOY --> EKS[EKS Cluster]
        DEPLOY --> S3[S3 + CloudFront]
        DEPLOY --> LAMBDA[Lambda Functions]
        
        EKS --> API[API Pods]
        EKS --> WORKER[Worker Pods]
        
        API --> RDS[(RDS MySQL)]
        WORKER --> RDS
        LAMBDA --> RDS
    end

    subgraph "Monitoring & Security"
        EKS --> CW[CloudWatch]
        API --> PROM[Prometheus]
        SECURITY --> GITHUB[GitHub Security]
    end
```

## Application Architecture

### Frontend Architecture (StencilJS)

```mermaid
graph LR
    subgraph "Frontend Components"
        USER[User] --> CDN[CloudFront CDN]
        CDN --> S3[S3 Static Hosting]
        S3 --> APP[StencilJS App]
        
        APP --> COMP1[Residual Master Component]
        APP --> COMP2[Data Entry Component]
        APP --> COMP3[Navigation Component]
    end

    subgraph "API Integration"
        APP --> API[REST API]
        API --> AUTH[Authentication]
        API --> DATA[Data Services]
    end
```

### Backend Architecture (.NET)

```mermaid
graph TB
    subgraph "API Layer"
        ALB[Application Load Balancer] --> API[ASP.NET Core API]
        API --> AUTH[Authentication Middleware]
        API --> CORS[CORS Middleware]
        API --> CTRL[Controllers]
    end

    subgraph "Business Logic"
        CTRL --> SVC[Services]
        SVC --> REPO[Repositories]
        SVC --> VALID[Validation]
    end

    subgraph "Data Layer"
        REPO --> EF[Entity Framework]
        EF --> RDS[(MySQL Database)]
    end

    subgraph "Background Processing"
        WORKER[Worker Service] --> QUEUE[Message Queue]
        WORKER --> EMAIL[Email Service]
        WORKER --> FILE[File Processing]
    end
```

## Infrastructure Architecture

### AWS Infrastructure

```mermaid
graph TB
    subgraph "Public Subnets"
        ALB[Application Load Balancer]
        NAT[NAT Gateway]
        IGW[Internet Gateway]
    end

    subgraph "Private Subnets"
        EKS[EKS Worker Nodes]
        LAMBDA[Lambda Functions]
    end

    subgraph "Database Subnets"
        RDS[(RDS MySQL)]
        REDIS[(ElastiCache Redis)]
    end

    subgraph "Security"
        WAF[AWS WAF]
        SG[Security Groups]
        NACL[Network ACLs]
    end

    IGW --> ALB
    ALB --> EKS
    EKS --> RDS
    EKS --> NAT
    NAT --> IGW
    
    WAF --> ALB
    SG --> EKS
    SG --> RDS
```

### Kubernetes Architecture

```mermaid
graph TB
    subgraph "Kubernetes Control Plane"
        MASTER[EKS Control Plane]
    end

    subgraph "Worker Nodes"
        NODE1[Worker Node 1]
        NODE2[Worker Node 2]
        NODE3[Worker Node 3]
    end

    subgraph "Namespaces"
        PROD[ais-production]
        STAGE[ais-staging]
        DEV[ais-dev]
        SYSTEM[kube-system]
    end

    subgraph "Workloads"
        API_POD[API Pods]
        WORKER_POD[Worker Pods]
        INGRESS[Ingress Controller]
        MONITOR[Monitoring Stack]
    end

    MASTER --> NODE1
    MASTER --> NODE2
    MASTER --> NODE3
    
    NODE1 --> API_POD
    NODE2 --> WORKER_POD
    NODE3 --> INGRESS
```

## CI/CD Architecture

### Pipeline Flow

```mermaid
graph LR
    subgraph "Source Control"
        GIT[Git Repository]
        BRANCH[Feature Branch]
        MAIN[Main Branch]
    end

    subgraph "Continuous Integration"
        TRIGGER[Webhook Trigger]
        DETECT[Change Detection]
        BUILD[Build Applications]
        TEST[Run Tests]
        SCAN[Security Scan]
        PACKAGE[Package Artifacts]
    end

    subgraph "Continuous Deployment"
        INFRA[Deploy Infrastructure]
        APP[Deploy Applications]
        VERIFY[Verify Deployment]
        NOTIFY[Notify Teams]
    end

    GIT --> TRIGGER
    TRIGGER --> DETECT
    DETECT --> BUILD
    BUILD --> TEST
    TEST --> SCAN
    SCAN --> PACKAGE
    PACKAGE --> INFRA
    INFRA --> APP
    APP --> VERIFY
    VERIFY --> NOTIFY
```

### Environment Strategy

```mermaid
graph TB
    subgraph "Development"
        DEV_BRANCH[develop branch] --> DEV_ENV[Development Environment]
        DEV_ENV --> DEV_TEST[Automated Testing]
    end

    subgraph "Staging"
        REL_BRANCH[release/* branch] --> STAGE_ENV[Staging Environment]
        STAGE_ENV --> STAGE_TEST[Integration Testing]
        STAGE_TEST --> UAT[User Acceptance Testing]
    end

    subgraph "Production"
        MAIN_BRANCH[main branch] --> PROD_ENV[Production Environment]
        PROD_ENV --> PROD_MONITOR[Production Monitoring]
    end

    DEV_TEST --> STAGE_ENV
    UAT --> PROD_ENV
```

## Security Architecture

### Security Layers

```mermaid
graph TB
    subgraph "Network Security"
        WAF[Web Application Firewall]
        VPC[Virtual Private Cloud]
        SG[Security Groups]
        NACL[Network ACLs]
    end

    subgraph "Application Security"
        AUTH[Authentication]
        AUTHZ[Authorization]
        ENCRYPT[Encryption]
        SECRETS[Secrets Management]
    end

    subgraph "Infrastructure Security"
        IAM[Identity & Access Management]
        KMS[Key Management Service]
        CLOUDTRAIL[CloudTrail Logging]
        CONFIG[AWS Config]
    end

    subgraph "Monitoring & Response"
        SIEM[Security Information & Event Management]
        ALERTS[Security Alerts]
        INCIDENT[Incident Response]
    end
```

### Data Flow Security

```mermaid
graph LR
    subgraph "External"
        USER[User] --> TLS[TLS 1.3]
    end

    subgraph "Edge"
        TLS --> WAF[AWS WAF]
        WAF --> CDN[CloudFront]
    end

    subgraph "Application"
        CDN --> ALB[Load Balancer]
        ALB --> JWT[JWT Authentication]
        JWT --> API[API Application]
    end

    subgraph "Data"
        API --> ENCRYPT_TRANSIT[Encryption in Transit]
        ENCRYPT_TRANSIT --> RDS[Encrypted Database]
    end
```

## Monitoring Architecture

### Observability Stack

```mermaid
graph TB
    subgraph "Metrics Collection"
        PROM[Prometheus]
        CW[CloudWatch]
        XRAY[AWS X-Ray]
    end

    subgraph "Log Aggregation"
        FLUENTD[Fluentd]
        CWLOGS[CloudWatch Logs]
        ELK[ELK Stack]
    end

    subgraph "Visualization"
        GRAFANA[Grafana]
        CWDASH[CloudWatch Dashboards]
        KIBANA[Kibana]
    end

    subgraph "Alerting"
        ALERT[Alert Manager]
        SNS[AWS SNS]
        SLACK[Slack Notifications]
    end

    PROM --> GRAFANA
    CW --> CWDASH
    FLUENTD --> ELK
    ELK --> KIBANA
    PROM --> ALERT
    ALERT --> SLACK
```

## Deployment Architecture

### Blue-Green Deployment

```mermaid
graph LR
    subgraph "Load Balancer"
        ALB[Application Load Balancer]
    end

    subgraph "Blue Environment"
        BLUE_API[API v1.0]
        BLUE_WORKER[Worker v1.0]
    end

    subgraph "Green Environment"
        GREEN_API[API v1.1]
        GREEN_WORKER[Worker v1.1]
    end

    subgraph "Database"
        RDS[(Shared Database)]
    end

    ALB --> BLUE_API
    ALB -.-> GREEN_API
    BLUE_API --> RDS
    GREEN_API --> RDS
    BLUE_WORKER --> RDS
    GREEN_WORKER --> RDS
```

### Rolling Deployment

```mermaid
graph TB
    subgraph "Deployment Process"
        START[Start Deployment] --> HEALTH[Health Check]
        HEALTH --> UPDATE1[Update Pod 1]
        UPDATE1 --> VERIFY1[Verify Pod 1]
        VERIFY1 --> UPDATE2[Update Pod 2]
        UPDATE2 --> VERIFY2[Verify Pod 2]
        VERIFY2 --> COMPLETE[Deployment Complete]
    end

    subgraph "Rollback Strategy"
        FAIL[Health Check Fail] --> ROLLBACK[Automatic Rollback]
        ROLLBACK --> PREVIOUS[Previous Version]
    end

    HEALTH --> FAIL
```

## Scalability Architecture

### Horizontal Pod Autoscaling

```mermaid
graph TB
    subgraph "Metrics"
        CPU[CPU Utilization]
        MEMORY[Memory Utilization]
        CUSTOM[Custom Metrics]
    end

    subgraph "HPA Controller"
        HPA[Horizontal Pod Autoscaler]
        DECISION[Scaling Decision]
    end

    subgraph "Scaling Actions"
        SCALE_UP[Scale Up Pods]
        SCALE_DOWN[Scale Down Pods]
        MAINTAIN[Maintain Current]
    end

    CPU --> HPA
    MEMORY --> HPA
    CUSTOM --> HPA
    HPA --> DECISION
    DECISION --> SCALE_UP
    DECISION --> SCALE_DOWN
    DECISION --> MAINTAIN
```

### Cluster Autoscaling

```mermaid
graph TB
    subgraph "Node Monitoring"
        PENDING[Pending Pods]
        RESOURCE[Resource Requests]
        UTILIZATION[Node Utilization]
    end

    subgraph "Cluster Autoscaler"
        CA[Cluster Autoscaler]
        NODE_DECISION[Node Scaling Decision]
    end

    subgraph "Node Actions"
        ADD_NODE[Add Worker Node]
        REMOVE_NODE[Remove Worker Node]
        NO_CHANGE[No Change]
    end

    PENDING --> CA
    RESOURCE --> CA
    UTILIZATION --> CA
    CA --> NODE_DECISION
    NODE_DECISION --> ADD_NODE
    NODE_DECISION --> REMOVE_NODE
    NODE_DECISION --> NO_CHANGE
```

## Disaster Recovery Architecture

### Backup Strategy

```mermaid
graph TB
    subgraph "Data Backup"
        RDS_BACKUP[RDS Automated Backups]
        SNAPSHOT[Manual Snapshots]
        CROSS_REGION[Cross-Region Replication]
    end

    subgraph "Application Backup"
        CONFIG_BACKUP[Configuration Backup]
        SECRET_BACKUP[Secrets Backup]
        IMAGE_BACKUP[Container Image Registry]
    end

    subgraph "Infrastructure Backup"
        TERRAFORM_STATE[Terraform State]
        INFRA_CODE[Infrastructure as Code]
        RUNBOOKS[Disaster Recovery Runbooks]
    end
```

### Recovery Procedures

```mermaid
graph LR
    subgraph "Incident Detection"
        MONITOR[Monitoring Alert]
        MANUAL[Manual Detection]
    end

    subgraph "Assessment"
        ASSESS[Impact Assessment]
        DECISION[Recovery Decision]
    end

    subgraph "Recovery Actions"
        FAILOVER[Database Failover]
        REDEPLOY[Application Redeployment]
        RESTORE[Data Restoration]
    end

    subgraph "Verification"
        VERIFY[Service Verification]
        NOTIFY[Stakeholder Notification]
    end

    MONITOR --> ASSESS
    MANUAL --> ASSESS
    ASSESS --> DECISION
    DECISION --> FAILOVER
    DECISION --> REDEPLOY
    DECISION --> RESTORE
    FAILOVER --> VERIFY
    REDEPLOY --> VERIFY
    RESTORE --> VERIFY
    VERIFY --> NOTIFY
```

## Technology Stack

### Frontend Stack
- **Framework**: StencilJS (TypeScript-based web components)
- **Build Tool**: Stencil Compiler
- **Testing**: Jest, Puppeteer
- **Styling**: SASS
- **Hosting**: AWS S3 + CloudFront

### Backend Stack
- **API Framework**: ASP.NET Core 9.0
- **Worker Service**: .NET 8.0 Worker Service
- **Database**: MySQL 8.0 with Entity Framework Core
- **Authentication**: JWT Bearer tokens
- **Hosting**: Amazon EKS (Kubernetes)

### Infrastructure Stack
- **Cloud Provider**: Amazon Web Services (AWS)
- **Container Orchestration**: Amazon EKS (Kubernetes)
- **Infrastructure as Code**: Terraform
- **Package Management**: Helm Charts
- **Service Mesh**: AWS Load Balancer Controller

### DevOps Stack
- **CI/CD**: GitHub Actions
- **Container Registry**: GitHub Container Registry (GHCR)
- **Security Scanning**: Trivy, Semgrep, CodeQL
- **Monitoring**: Prometheus, Grafana, CloudWatch
- **Logging**: Fluentd, CloudWatch Logs

## Performance Characteristics

### Scalability Targets
- **API**: 1000 requests/second with <200ms response time
- **Database**: Support for 100 concurrent connections
- **Frontend**: <2 second initial load time
- **Worker Service**: Process 10,000 jobs/hour

### Availability Targets
- **Production**: 99.9% uptime (8.76 hours downtime/year)
- **Staging**: 99% uptime
- **Development**: Best effort

### Security Standards
- **Encryption**: TLS 1.3 for all external communications
- **Authentication**: Multi-factor authentication required
- **Authorization**: Role-based access control (RBAC)
- **Compliance**: SOC 2 Type II, PCI DSS (if applicable)

## Future Considerations

### Planned Enhancements
1. **Microservices Migration**: Break down monolithic API into microservices
2. **Event-Driven Architecture**: Implement event sourcing and CQRS patterns
3. **Multi-Region Deployment**: Expand to multiple AWS regions
4. **Advanced Monitoring**: Implement distributed tracing with Jaeger
5. **Machine Learning**: Add ML-based anomaly detection

### Technology Roadmap
- **Q1 2024**: Complete initial deployment and stabilization
- **Q2 2024**: Implement advanced monitoring and alerting
- **Q3 2024**: Add multi-region support
- **Q4 2024**: Begin microservices migration

This architecture provides a solid foundation for the AIS Modernization project with built-in scalability, security, and maintainability features.

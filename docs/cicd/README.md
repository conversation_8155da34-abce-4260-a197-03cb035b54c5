# CI/CD Pipeline Documentation

This document provides comprehensive information about the CI/CD pipeline implementation for the AIS Modernization project.

## Overview

The CI/CD pipeline is built using GitHub Actions and provides:

- **Multi-project builds** with change detection
- **Automated testing** with coverage reporting
- **Security scanning** (SAST, DAST, dependency scanning)
- **Container builds** and registry management
- **Infrastructure as Code** deployment
- **Environment-specific deployments**
- **Monitoring and alerting**

## Pipeline Architecture

```mermaid
graph TD
    A[Code Push/PR] --> B[Detect Changes]
    B --> C[Security Scan]
    B --> D[Build StencilJS App]
    B --> E[Build .NET API]
    B --> F[Build Worker Service]
    B --> G[Build Lambda]
    
    D --> H[Build Containers]
    E --> H
    F --> H
    
    H --> I[Deploy Infrastructure]
    I --> J[Deploy Applications]
    J --> K[Verify Deployment]
    K --> L[Notify Teams]
```

## Workflows

### 1. Main CI/CD Pipeline (`.github/workflows/ci-cd-main.yml`)

**Triggers:**
- Push to `main`, `develop`, `release/*`, `hotfix/*` branches
- Pull requests to `main`, `develop`
- Manual workflow dispatch

**Jobs:**
- `detect-changes`: Analyzes changed files to determine what needs to be built
- `security-scan`: Runs comprehensive security scanning
- `build-*`: Builds each application component
- `build-containers`: Creates Docker images for containerized applications

**Features:**
- **Change Detection**: Only builds components that have changed
- **Matrix Strategy**: Parallel builds for different components
- **Artifact Management**: Stores build outputs for deployment
- **Security Integration**: Uploads results to GitHub Security tab

### 2. Deployment Pipeline (`.github/workflows/deploy.yml`)

**Triggers:**
- Successful completion of main CI/CD pipeline
- Manual workflow dispatch with environment selection

**Jobs:**
- `setup-deployment`: Determines target environment and deployment strategy
- `deploy-infrastructure`: Provisions AWS infrastructure using Terraform
- `deploy-frontend`: Deploys StencilJS app to S3/CloudFront
- `deploy-api`: Deploys .NET API to EKS using Helm
- `deploy-worker-service`: Deploys Worker Service to EKS using Helm
- `deploy-lambda`: Deploys Lambda function to AWS
- `verify-deployment`: Runs health checks and smoke tests

## Environment Strategy

### Development Environment
- **Branch**: `develop`
- **Infrastructure**: Cost-optimized (SPOT instances, minimal monitoring)
- **Database**: Small RDS instance with short backup retention
- **Deployment**: Automatic on successful builds
- **Access**: Open for development team

### Staging Environment
- **Branch**: `release/*` branches
- **Infrastructure**: Production-like but smaller scale
- **Database**: Production-like configuration
- **Deployment**: Automatic on successful builds
- **Access**: Restricted to QA and stakeholders

### Production Environment
- **Branch**: `main`
- **Infrastructure**: High availability, monitoring, and security
- **Database**: Multi-AZ, encrypted, long backup retention
- **Deployment**: Manual approval required
- **Access**: Highly restricted

## Security Scanning

### Static Application Security Testing (SAST)
- **Tool**: Semgrep
- **Coverage**: Security vulnerabilities, secrets, OWASP Top 10
- **Integration**: Results uploaded to GitHub Security tab

### Dependency Scanning
- **Tool**: Trivy
- **Coverage**: Known vulnerabilities in dependencies
- **Languages**: .NET (NuGet), Node.js (npm)

### Container Scanning
- **Tool**: Trivy
- **Coverage**: OS vulnerabilities, misconfigurations
- **Integration**: Scans all built container images

### Infrastructure Scanning
- **Tool**: Checkov
- **Coverage**: Terraform configurations, Kubernetes manifests
- **Policies**: CIS benchmarks, security best practices

## Secrets Management

### GitHub Secrets
Required secrets for the pipeline:

```bash
# AWS Configuration
AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY

# Database
DATABASE_CONNECTION_STRING

# Email Configuration
SMTP_SERVER
SMTP_USER
SMTP_PASSWORD

# Notifications
SLACK_WEBHOOK
```

### Environment Variables
Required variables per environment:

```bash
# AWS Configuration
AWS_REGION=us-east-1
EKS_CLUSTER_NAME=ais-modernization-dev
S3_BUCKET_NAME=ais-frontend
CLOUDFRONT_DISTRIBUTION_ID=E1234567890

# Application URLs
API_BASE_URL=https://api-dev.yourcompany.com
FRONTEND_URL=https://frontend-dev.yourcompany.com
```

## Monitoring and Alerting

### Pipeline Monitoring
- **GitHub Actions**: Built-in workflow monitoring
- **Notifications**: Slack integration for deployment status
- **Metrics**: Build times, success rates, deployment frequency

### Application Monitoring
- **CloudWatch**: AWS infrastructure and application metrics
- **Prometheus**: Kubernetes cluster metrics
- **Grafana**: Visualization and alerting

### Security Monitoring
- **GitHub Security**: Vulnerability alerts and dependency updates
- **AWS Security Hub**: Centralized security findings
- **CloudTrail**: API activity monitoring

## Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Check workflow logs
gh run list --workflow=ci-cd-main.yml
gh run view <run-id>

# Debug specific job
gh run view <run-id> --job=<job-id>
```

#### 2. Deployment Failures
```bash
# Check Terraform state
terraform state list
terraform plan

# Check Kubernetes resources
kubectl get pods -n ais-dev
kubectl describe pod <pod-name> -n ais-dev
kubectl logs <pod-name> -n ais-dev
```

#### 3. Security Scan Failures
```bash
# Review security reports
# Check GitHub Security tab
# Review SARIF files in workflow artifacts
```

### Debug Commands

#### Pipeline Debugging
```bash
# Enable debug logging
# Add to workflow:
env:
  ACTIONS_STEP_DEBUG: true
  ACTIONS_RUNNER_DEBUG: true
```

#### Infrastructure Debugging
```bash
# Terraform debugging
export TF_LOG=DEBUG
terraform plan -detailed-exitcode

# Kubernetes debugging
kubectl get events --sort-by=.metadata.creationTimestamp
kubectl describe node <node-name>
```

## Best Practices

### 1. Branch Protection
- Require pull request reviews
- Require status checks to pass
- Require branches to be up to date
- Restrict pushes to main branch

### 2. Security
- Use least privilege access
- Rotate secrets regularly
- Enable dependency updates
- Monitor security alerts

### 3. Performance
- Use caching for dependencies
- Optimize Docker builds with multi-stage
- Use change detection to avoid unnecessary builds
- Parallelize independent jobs

### 4. Reliability
- Implement proper health checks
- Use rolling deployments
- Set up monitoring and alerting
- Plan for rollback scenarios

## Maintenance

### Regular Tasks
- Update GitHub Actions versions
- Update base Docker images
- Review and update security policies
- Monitor and optimize costs

### Quarterly Reviews
- Review pipeline performance metrics
- Update security scanning rules
- Review and update documentation
- Conduct disaster recovery tests

## Support

For pipeline issues or questions:
- Create an issue in the repository
- Contact the DevOps team via Slack: `#devops`
- Email: <EMAIL>

## References

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [Helm Documentation](https://helm.sh/docs/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)

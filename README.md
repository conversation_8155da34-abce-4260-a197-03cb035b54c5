# AIS Modernization

This repository contains all the artifacts to enable the modernization of AIS 1.0, including comprehensive CI/CD pipelines, infrastructure as code, and DevSecOps best practices.

## Repository Structure

```
├── GenAI PoCs/
│   ├── stencil-residual-app/           # StencilJS Frontend Application
│   ├── stencil-residual-app-api/       # ASP.NET Core Web API
│   └── ProcessingApp/
│       └── VehicleExportWorkerService/ # .NET Worker Service & AWS Lambda
├── .github/
│   ├── workflows/                      # GitHub Actions CI/CD Pipelines
│   ├── actions/                        # Reusable Composite Actions
│   └── templates/                      # Issue and PR Templates
├── infrastructure/
│   ├── terraform/                      # Infrastructure as Code
│   ├── helm/                          # Kubernetes Helm Charts
│   └── docker/                        # Docker Configurations
├── scripts/                           # Automation Scripts
└── docs/                             # Documentation
```

## Applications Overview

### 1. StencilJS Frontend Application
- **Technology**: StencilJS (TypeScript-based web components)
- **Testing**: Jest with coverage reporting
- **Build**: Stencil build system with SASS support
- **Features**: Component-based architecture, responsive UI

### 2. ASP.NET Core Web API
- **Technology**: .NET 9.0, ASP.NET Core Web API
- **Database**: MySQL with Entity Framework Core
- **Features**: Swagger/OpenAPI, CORS enabled, RESTful API

### 3. Worker Service
- **Technology**: .NET 8.0 Worker Service
- **Database**: MySQL with Dapper
- **Features**: Background processing, email notifications, scheduled tasks

### 4. AWS Lambda Function
- **Technology**: .NET 8.0 AWS Lambda
- **Database**: MySQL with Dapper
- **Features**: Serverless processing, event-driven architecture

## CI/CD Pipeline Features

- ✅ **Multi-project builds** with matrix strategy
- ✅ **Environment-specific deployments** (dev, staging, production)
- ✅ **Automated testing** with coverage reporting
- ✅ **Security scanning** (SAST, DAST, dependency scanning)
- ✅ **Infrastructure as Code** with Terraform
- ✅ **Container orchestration** with Kubernetes/Helm
- ✅ **Secrets management** with GitHub Secrets
- ✅ **Artifact management** and versioning
- ✅ **DevSecOps best practices** integration

## Quick Start

### Prerequisites
- Node.js 18+ (for StencilJS app)
- .NET 8.0+ SDK (for .NET applications)
- Docker (for containerization)
- Terraform (for infrastructure)
- kubectl (for Kubernetes deployments)

### Local Development
```bash
# Clone the repository
git clone <repository-url>
cd ais-modernization

# Setup StencilJS Frontend
cd "GenAI PoCs/stencil-residual-app"
npm install
npm start

# Setup .NET API
cd "../stencil-residual-app-api"
dotnet restore
dotnet run

# Setup Worker Service
cd "../ProcessingApp/VehicleExportWorkerService/src/VehicleExportWorkerService"
dotnet restore
dotnet run
```

### Deployment
The CI/CD pipeline automatically handles deployments based on branch and environment configurations. See [CI/CD Documentation](docs/cicd/README.md) for detailed information.

## Documentation

- [CI/CD Pipeline Guide](docs/cicd/README.md)
- [Infrastructure Setup](docs/infrastructure/README.md)
- [Security Guidelines](docs/security/README.md)
- [Development Guide](docs/development/README.md)
- [Troubleshooting](docs/troubleshooting/README.md)

## Contributing

Please read our [Contributing Guidelines](docs/CONTRIBUTING.md) before submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

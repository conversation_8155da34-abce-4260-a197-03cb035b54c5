#!/bin/bash

# Setup Infrastructure Script
# This script initializes the Terraform backend and sets up the basic infrastructure

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION=${AWS_REGION:-"us-east-1"}
PROJECT_NAME="ais-modernization"
ENVIRONMENTS=("dev" "staging" "production")

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if Terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install it first."
        exit 1
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed. Please install it first."
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials are not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    log_success "All prerequisites are met."
}

create_s3_backend() {
    local env=$1
    local bucket_name="${PROJECT_NAME}-terraform-state-${env}"
    local dynamodb_table="${PROJECT_NAME}-terraform-locks"
    
    log_info "Creating S3 backend for environment: ${env}"
    
    # Create S3 bucket for Terraform state
    if aws s3 ls "s3://${bucket_name}" 2>&1 | grep -q 'NoSuchBucket'; then
        log_info "Creating S3 bucket: ${bucket_name}"
        aws s3 mb "s3://${bucket_name}" --region "${AWS_REGION}"
        
        # Enable versioning
        aws s3api put-bucket-versioning \
            --bucket "${bucket_name}" \
            --versioning-configuration Status=Enabled
        
        # Enable server-side encryption
        aws s3api put-bucket-encryption \
            --bucket "${bucket_name}" \
            --server-side-encryption-configuration '{
                "Rules": [
                    {
                        "ApplyServerSideEncryptionByDefault": {
                            "SSEAlgorithm": "AES256"
                        }
                    }
                ]
            }'
        
        # Block public access
        aws s3api put-public-access-block \
            --bucket "${bucket_name}" \
            --public-access-block-configuration \
            BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true
        
        log_success "S3 bucket created: ${bucket_name}"
    else
        log_info "S3 bucket already exists: ${bucket_name}"
    fi
    
    # Create DynamoDB table for state locking (only once)
    if [ "${env}" == "dev" ]; then
        if ! aws dynamodb describe-table --table-name "${dynamodb_table}" &> /dev/null; then
            log_info "Creating DynamoDB table: ${dynamodb_table}"
            aws dynamodb create-table \
                --table-name "${dynamodb_table}" \
                --attribute-definitions AttributeName=LockID,AttributeType=S \
                --key-schema AttributeName=LockID,KeyType=HASH \
                --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
                --region "${AWS_REGION}"
            
            # Wait for table to be created
            aws dynamodb wait table-exists --table-name "${dynamodb_table}" --region "${AWS_REGION}"
            log_success "DynamoDB table created: ${dynamodb_table}"
        else
            log_info "DynamoDB table already exists: ${dynamodb_table}"
        fi
    fi
}

initialize_terraform() {
    local env=$1
    local terraform_dir="infrastructure/terraform/environments/${env}"
    
    log_info "Initializing Terraform for environment: ${env}"
    
    if [ ! -d "${terraform_dir}" ]; then
        log_error "Terraform directory not found: ${terraform_dir}"
        exit 1
    fi
    
    cd "${terraform_dir}"
    
    # Initialize Terraform
    terraform init
    
    # Validate configuration
    terraform validate
    
    # Format code
    terraform fmt -recursive
    
    log_success "Terraform initialized for environment: ${env}"
    
    cd - > /dev/null
}

create_github_secrets() {
    log_info "Creating GitHub secrets template..."
    
    cat > github-secrets-template.txt << 'EOF'
# GitHub Secrets Configuration
# Please add these secrets to your GitHub repository settings

## AWS Configuration
AWS_ACCESS_KEY_ID=<your-aws-access-key-id>
AWS_SECRET_ACCESS_KEY=<your-aws-secret-access-key>

## Database Configuration
DATABASE_CONNECTION_STRING=<your-database-connection-string>

## Email Configuration
SMTP_SERVER=smtp-sdc.coxautoinc.com
SMTP_USER=smtp
SMTP_PASSWORD=<your-smtp-password>

## Notifications
SLACK_WEBHOOK=<your-slack-webhook-url>

## Security Scanning
SEMGREP_APP_TOKEN=<your-semgrep-token>

## Container Registry
GITHUB_TOKEN=<automatically-provided-by-github>
EOF
    
    log_success "GitHub secrets template created: github-secrets-template.txt"
}

create_environment_variables() {
    log_info "Creating environment variables template..."
    
    for env in "${ENVIRONMENTS[@]}"; do
        cat > "github-env-vars-${env}.txt" << EOF
# GitHub Environment Variables for ${env}
# Please add these variables to your GitHub repository environment settings

## AWS Configuration
AWS_REGION=${AWS_REGION}
EKS_CLUSTER_NAME=${PROJECT_NAME}-${env}
S3_BUCKET_NAME=${PROJECT_NAME}-frontend-${env}

## Application URLs (update with your actual domains)
API_BASE_URL=https://api-${env}.yourcompany.com
FRONTEND_URL=https://frontend-${env}.yourcompany.com

## CloudFront (will be populated after infrastructure deployment)
CLOUDFRONT_DISTRIBUTION_ID=<to-be-populated>
EOF
    done
    
    log_success "Environment variables templates created for all environments"
}

setup_pre_commit_hooks() {
    log_info "Setting up pre-commit hooks..."
    
    # Create pre-commit configuration
    cat > .pre-commit-config.yaml << 'EOF'
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
      - id: detect-private-key

  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.81.0
    hooks:
      - id: terraform_fmt
      - id: terraform_validate
      - id: terraform_docs
      - id: terraform_tflint

  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']

  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
EOF
    
    # Install pre-commit if available
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        log_success "Pre-commit hooks installed"
    else
        log_warning "pre-commit not installed. Please install it and run 'pre-commit install'"
    fi
}

create_makefile() {
    log_info "Creating Makefile for common operations..."
    
    cat > Makefile << 'EOF'
.PHONY: help init plan apply destroy test security-scan format validate

# Default environment
ENV ?= dev

help: ## Show this help message
	@echo 'Usage: make [target] [ENV=environment]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

init: ## Initialize Terraform for specified environment
	@echo "Initializing Terraform for $(ENV) environment..."
	cd infrastructure/terraform/environments/$(ENV) && terraform init

plan: ## Plan Terraform changes for specified environment
	@echo "Planning Terraform changes for $(ENV) environment..."
	cd infrastructure/terraform/environments/$(ENV) && terraform plan -var-file="terraform.tfvars"

apply: ## Apply Terraform changes for specified environment
	@echo "Applying Terraform changes for $(ENV) environment..."
	cd infrastructure/terraform/environments/$(ENV) && terraform apply -var-file="terraform.tfvars"

destroy: ## Destroy Terraform resources for specified environment
	@echo "Destroying Terraform resources for $(ENV) environment..."
	cd infrastructure/terraform/environments/$(ENV) && terraform destroy -var-file="terraform.tfvars"

test: ## Run tests for all applications
	@echo "Running tests..."
	cd "GenAI PoCs/stencil-residual-app" && npm test
	cd "GenAI PoCs/stencil-residual-app-api" && dotnet test
	cd "GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src" && dotnet test

security-scan: ## Run security scans
	@echo "Running security scans..."
	docker run --rm -v $(PWD):/src aquasec/trivy fs /src

format: ## Format Terraform and other code
	@echo "Formatting code..."
	terraform fmt -recursive infrastructure/terraform/
	cd "GenAI PoCs/stencil-residual-app" && npm run format || echo "No format script found"

validate: ## Validate Terraform configurations
	@echo "Validating Terraform configurations..."
	cd infrastructure/terraform/environments/$(ENV) && terraform validate

build-containers: ## Build all container images
	@echo "Building container images..."
	docker build -f infrastructure/docker/api/Dockerfile "GenAI PoCs/stencil-residual-app-api" -t ais-api:latest
	docker build -f infrastructure/docker/worker-service/Dockerfile "GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src" -t ais-worker:latest

setup-dev: ## Setup development environment
	@echo "Setting up development environment..."
	cd "GenAI PoCs/stencil-residual-app" && npm install
	cd "GenAI PoCs/stencil-residual-app-api" && dotnet restore
	cd "GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src" && dotnet restore
EOF
    
    log_success "Makefile created"
}

main() {
    log_info "Starting infrastructure setup for ${PROJECT_NAME}..."
    
    # Check prerequisites
    check_prerequisites
    
    # Create S3 backends for all environments
    for env in "${ENVIRONMENTS[@]}"; do
        create_s3_backend "${env}"
    done
    
    # Initialize Terraform for all environments
    for env in "${ENVIRONMENTS[@]}"; do
        initialize_terraform "${env}"
    done
    
    # Create GitHub configuration templates
    create_github_secrets
    create_environment_variables
    
    # Setup development tools
    setup_pre_commit_hooks
    create_makefile
    
    log_success "Infrastructure setup completed successfully!"
    
    echo ""
    log_info "Next steps:"
    echo "1. Add the secrets from 'github-secrets-template.txt' to your GitHub repository"
    echo "2. Add the environment variables from 'github-env-vars-*.txt' to your GitHub environments"
    echo "3. Review and customize the Terraform variables in each environment"
    echo "4. Run 'make plan ENV=dev' to see what will be created"
    echo "5. Run 'make apply ENV=dev' to create the development infrastructure"
    echo "6. Configure your applications with the created infrastructure endpoints"
    echo ""
    log_warning "Remember to review all configurations before applying to production!"
}

# Run main function
main "$@"

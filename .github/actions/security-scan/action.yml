name: 'Security Scanning'
description: 'Comprehensive security scanning for applications'

inputs:
  scan-type:
    description: 'Type of scan to perform (sast, dast, dependency, container, all)'
    required: false
    default: 'all'
  working-directory:
    description: 'Working directory for scanning'
    required: false
    default: '.'
  image-name:
    description: 'Container image name for container scanning'
    required: false
    default: ''
  severity-threshold:
    description: 'Minimum severity level to report (LOW, MEDIUM, HIGH, CRITICAL)'
    required: false
    default: 'MEDIUM'
  fail-on-issues:
    description: 'Whether to fail the build on security issues'
    required: false
    default: 'false'

outputs:
  issues-found:
    description: 'Whether security issues were found'
    value: ${{ steps.summary.outputs.issues-found }}
  report-path:
    description: 'Path to the security report'
    value: ${{ steps.summary.outputs.report-path }}

runs:
  using: 'composite'
  steps:
    - name: Create reports directory
      shell: bash
      run: mkdir -p security-reports

    # SAST Scanning with Semgrep
    - name: Run SAST scan with Semgrep
      if: inputs.scan-type == 'sast' || inputs.scan-type == 'all'
      uses: returntocorp/semgrep-action@v1
      with:
        config: >-
          p/security-audit
          p/secrets
          p/owasp-top-ten
        generateSarif: "1"
        publishToken: ${{ env.SEMGREP_APP_TOKEN }}
      continue-on-error: true

    # Dependency Scanning with Trivy
    - name: Run dependency scan
      if: inputs.scan-type == 'dependency' || inputs.scan-type == 'all'
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: ${{ inputs.working-directory }}
        format: 'sarif'
        output: 'security-reports/trivy-dependency-results.sarif'
        severity: ${{ inputs.severity-threshold }}
      continue-on-error: true

    # Container Scanning with Trivy
    - name: Run container scan
      if: (inputs.scan-type == 'container' || inputs.scan-type == 'all') && inputs.image-name != ''
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ inputs.image-name }}
        format: 'sarif'
        output: 'security-reports/trivy-container-results.sarif'
        severity: ${{ inputs.severity-threshold }}
      continue-on-error: true

    # Secret Scanning with TruffleHog
    - name: Run secret scan
      if: inputs.scan-type == 'secrets' || inputs.scan-type == 'all'
      uses: trufflesecurity/trufflehog@main
      with:
        path: ${{ inputs.working-directory }}
        base: main
        head: HEAD
        extra_args: --debug --only-verified
      continue-on-error: true

    # License Scanning
    - name: Run license scan
      if: inputs.scan-type == 'license' || inputs.scan-type == 'all'
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        # Install license checker for Node.js projects
        if [ -f "package.json" ]; then
          npx license-checker --json --out security-reports/licenses.json || true
        fi
        
        # Check for .NET projects
        if [ -f "*.csproj" ] || [ -f "*.sln" ]; then
          echo "License scanning for .NET projects not implemented yet" > security-reports/dotnet-licenses.txt
        fi
      continue-on-error: true

    # Infrastructure Scanning with Checkov
    - name: Run infrastructure scan
      if: inputs.scan-type == 'infrastructure' || inputs.scan-type == 'all'
      uses: bridgecrewio/checkov-action@master
      with:
        directory: ${{ inputs.working-directory }}
        framework: terraform,dockerfile,kubernetes
        output_format: sarif
        output_file_path: security-reports/checkov-results.sarif
        soft_fail: true
      continue-on-error: true

    # Upload SARIF results to GitHub Security tab
    - name: Upload SARIF results
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: security-reports/
      continue-on-error: true

    # Generate summary report
    - name: Generate security summary
      id: summary
      shell: bash
      run: |
        echo "Generating security scan summary..."
        
        ISSUES_FOUND=false
        REPORT_PATH="security-reports/summary.md"
        
        # Create summary report
        cat > $REPORT_PATH << 'EOF'
        # Security Scan Summary
        
        ## Scan Results
        
        EOF
        
        # Check for SARIF files and count issues
        for sarif_file in security-reports/*.sarif; do
          if [ -f "$sarif_file" ]; then
            echo "Processing $sarif_file..."
            # Simple check for results (this could be enhanced with jq)
            if grep -q '"level"' "$sarif_file" 2>/dev/null; then
              ISSUES_FOUND=true
              echo "- ⚠️ Issues found in $(basename $sarif_file)" >> $REPORT_PATH
            else
              echo "- ✅ No issues in $(basename $sarif_file)" >> $REPORT_PATH
            fi
          fi
        done
        
        echo "issues-found=$ISSUES_FOUND" >> $GITHUB_OUTPUT
        echo "report-path=$REPORT_PATH" >> $GITHUB_OUTPUT
        
        # Display summary
        cat $REPORT_PATH
        
        # Fail if issues found and fail-on-issues is true
        if [ "$ISSUES_FOUND" = "true" ] && [ "${{ inputs.fail-on-issues }}" = "true" ]; then
          echo "Security issues found and fail-on-issues is enabled"
          exit 1
        fi

    # Upload security reports as artifacts
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-reports-${{ github.run_id }}
        path: security-reports/
        retention-days: 30

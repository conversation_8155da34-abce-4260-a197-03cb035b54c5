name: 'Setup .NET Environment'
description: 'Sets up .NET SDK with caching and common configurations'

inputs:
  dotnet-version:
    description: '.NET version to setup'
    required: false
    default: '8.0.x'
  include-prerelease:
    description: 'Include prerelease versions'
    required: false
    default: 'false'
  cache-key-suffix:
    description: 'Additional suffix for cache key'
    required: false
    default: ''
  working-directory:
    description: 'Working directory for the project'
    required: false
    default: '.'

outputs:
  dotnet-version:
    description: 'The installed .NET version'
    value: ${{ steps.setup.outputs.dotnet-version }}
  cache-hit:
    description: 'Whether cache was hit'
    value: ${{ steps.cache.outputs.cache-hit }}

runs:
  using: 'composite'
  steps:
    - name: Setup .NET
      id: setup
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ inputs.dotnet-version }}
        include-prerelease: ${{ inputs.include-prerelease }}

    - name: Cache NuGet packages
      id: cache
      uses: actions/cache@v4
      with:
        path: |
          ~/.nuget/packages
          ${{ inputs.working-directory }}/obj
          ${{ inputs.working-directory }}/bin
        key: ${{ runner.os }}-nuget-${{ hashFiles(format('{0}/**/*.csproj', inputs.working-directory)) }}-${{ inputs.cache-key-suffix }}
        restore-keys: |
          ${{ runner.os }}-nuget-${{ hashFiles(format('{0}/**/*.csproj', inputs.working-directory)) }}-
          ${{ runner.os }}-nuget-

    - name: Display .NET info
      shell: bash
      run: |
        echo "Installed .NET version:"
        dotnet --version
        echo "Available SDKs:"
        dotnet --list-sdks
        echo "Available runtimes:"
        dotnet --list-runtimes

    - name: Restore dependencies
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        echo "Restoring NuGet packages..."
        dotnet restore --verbosity minimal

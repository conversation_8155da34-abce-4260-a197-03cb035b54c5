name: 'Setup Node.js Environment'
description: 'Sets up Node.js with caching and common configurations'

inputs:
  node-version:
    description: 'Node.js version to setup'
    required: false
    default: '18'
  package-manager:
    description: 'Package manager to use (npm, yarn, pnpm)'
    required: false
    default: 'npm'
  working-directory:
    description: 'Working directory for the project'
    required: false
    default: '.'
  cache-key-suffix:
    description: 'Additional suffix for cache key'
    required: false
    default: ''
  install-dependencies:
    description: 'Whether to install dependencies'
    required: false
    default: 'true'

outputs:
  node-version:
    description: 'The installed Node.js version'
    value: ${{ steps.setup.outputs.node-version }}
  cache-hit:
    description: 'Whether cache was hit'
    value: ${{ steps.cache.outputs.cache-hit }}

runs:
  using: 'composite'
  steps:
    - name: Setup Node.js
      id: setup
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: ${{ inputs.package-manager }}
        cache-dependency-path: ${{ inputs.working-directory }}/package-lock.json

    - name: Cache node_modules
      id: cache
      uses: actions/cache@v4
      with:
        path: |
          ${{ inputs.working-directory }}/node_modules
          ~/.npm
          ~/.cache/yarn
          ~/.cache/pnpm
        key: ${{ runner.os }}-${{ inputs.package-manager }}-${{ hashFiles(format('{0}/package-lock.json', inputs.working-directory), format('{0}/yarn.lock', inputs.working-directory), format('{0}/pnpm-lock.yaml', inputs.working-directory)) }}-${{ inputs.cache-key-suffix }}
        restore-keys: |
          ${{ runner.os }}-${{ inputs.package-manager }}-${{ hashFiles(format('{0}/package-lock.json', inputs.working-directory), format('{0}/yarn.lock', inputs.working-directory), format('{0}/pnpm-lock.yaml', inputs.working-directory)) }}-
          ${{ runner.os }}-${{ inputs.package-manager }}-

    - name: Display Node.js info
      shell: bash
      run: |
        echo "Installed Node.js version:"
        node --version
        echo "Installed npm version:"
        npm --version

    - name: Install dependencies
      if: inputs.install-dependencies == 'true'
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        case "${{ inputs.package-manager }}" in
          npm)
            echo "Installing dependencies with npm..."
            npm ci
            ;;
          yarn)
            echo "Installing dependencies with yarn..."
            yarn install --frozen-lockfile
            ;;
          pnpm)
            echo "Installing dependencies with pnpm..."
            pnpm install --frozen-lockfile
            ;;
          *)
            echo "Unknown package manager: ${{ inputs.package-manager }}"
            exit 1
            ;;
        esac

    - name: Audit dependencies
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        case "${{ inputs.package-manager }}" in
          npm)
            npm audit --audit-level=high || echo "Audit found issues, but continuing..."
            ;;
          yarn)
            yarn audit --level high || echo "Audit found issues, but continuing..."
            ;;
          pnpm)
            pnpm audit --audit-level high || echo "Audit found issues, but continuing..."
            ;;
        esac

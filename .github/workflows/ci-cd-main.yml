name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'release/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - production
      skip_tests:
        description: 'Skip tests'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '18'
  DOTNET_VERSION: '8.0.x'
  DOTNET_VERSION_9: '9.0.x'

jobs:
  # Job to detect changes and set up build matrix
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      stencil-app: ${{ steps.changes.outputs.stencil-app }}
      api: ${{ steps.changes.outputs.api }}
      worker-service: ${{ steps.changes.outputs.worker-service }}
      lambda: ${{ steps.changes.outputs.lambda }}
      infrastructure: ${{ steps.changes.outputs.infrastructure }}
      any-app-changed: ${{ steps.changes.outputs.any-app-changed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Detect changes
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            stencil-app:
              - 'GenAI PoCs/stencil-residual-app/**'
            api:
              - 'GenAI PoCs/stencil-residual-app-api/**'
            worker-service:
              - 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src/VehicleExportWorkerService/**'
            lambda:
              - 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src/DescribedVehicleExportWorker/**'
            infrastructure:
              - 'infrastructure/**'
              - '.github/workflows/**'
            any-app-changed:
              - 'GenAI PoCs/**'

  # Security scanning job
  security-scan:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.any-app-changed == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: 'csharp,javascript'

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # StencilJS Frontend Build and Test
  build-stencil-app:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.stencil-app == 'true'
    defaults:
      run:
        working-directory: 'GenAI PoCs/stencil-residual-app'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'GenAI PoCs/stencil-residual-app/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint || echo "Linting not configured"

      - name: Run tests
        if: ${{ !inputs.skip_tests }}
        run: npm run test:coverage

      - name: Upload coverage reports
        if: ${{ !inputs.skip_tests }}
        uses: codecov/codecov-action@v4
        with:
          file: ./GenAI PoCs/stencil-residual-app/coverage/lcov.info
          flags: stencil-app

      - name: Build application
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: stencil-app-build
          path: 'GenAI PoCs/stencil-residual-app/dist'
          retention-days: 30

  # .NET API Build and Test
  build-api:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.api == 'true'
    defaults:
      run:
        working-directory: 'GenAI PoCs/stencil-residual-app-api'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION_9 }}

      - name: Restore dependencies
        run: dotnet restore

      - name: Build application
        run: dotnet build --no-restore --configuration Release

      - name: Run tests
        if: ${{ !inputs.skip_tests }}
        run: dotnet test --no-build --configuration Release --collect:"XPlat Code Coverage" --results-directory ./coverage

      - name: Upload coverage reports
        if: ${{ !inputs.skip_tests }}
        uses: codecov/codecov-action@v4
        with:
          directory: ./GenAI PoCs/stencil-residual-app-api/coverage
          flags: api

      - name: Publish application
        run: dotnet publish --no-build --configuration Release --output ./publish

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: api-build
          path: 'GenAI PoCs/stencil-residual-app-api/publish'
          retention-days: 30

  # Worker Service Build and Test
  build-worker-service:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.worker-service == 'true'
    defaults:
      run:
        working-directory: 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Restore dependencies
        run: dotnet restore VehicleExportWorkerService.sln

      - name: Build application
        run: dotnet build VehicleExportWorkerService.sln --no-restore --configuration Release

      - name: Run tests
        if: ${{ !inputs.skip_tests }}
        run: dotnet test VehicleExportWorkerService.sln --no-build --configuration Release --collect:"XPlat Code Coverage" --results-directory ./coverage

      - name: Upload coverage reports
        if: ${{ !inputs.skip_tests }}
        uses: codecov/codecov-action@v4
        with:
          directory: ./GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src/coverage
          flags: worker-service

      - name: Publish application
        run: dotnet publish VehicleExportWorkerService/VehicleExportWorkerService.csproj --no-build --configuration Release --output ./publish

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: worker-service-build
          path: 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src/publish'
          retention-days: 30

  # AWS Lambda Build and Test
  build-lambda:
    runs-on: ubuntu-latest
    needs: detect-changes
    if: needs.detect-changes.outputs.lambda == 'true'
    defaults:
      run:
        working-directory: 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src/DescribedVehicleExportWorker'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Install Lambda tools
        run: dotnet tool install -g Amazon.Lambda.Tools

      - name: Restore dependencies
        run: dotnet restore

      - name: Build application
        run: dotnet build --no-restore --configuration Release

      - name: Package Lambda
        run: dotnet lambda package --configuration Release --output-package ./lambda-package.zip

      - name: Upload Lambda package
        uses: actions/upload-artifact@v4
        with:
          name: lambda-package
          path: 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src/DescribedVehicleExportWorker/lambda-package.zip'
          retention-days: 30

  # Container Build Job
  build-containers:
    runs-on: ubuntu-latest
    needs: [detect-changes, build-api, build-worker-service]
    if: always() && (needs.detect-changes.outputs.api == 'true' || needs.detect-changes.outputs.worker-service == 'true')
    strategy:
      matrix:
        include:
          - app: api
            context: 'GenAI PoCs/stencil-residual-app-api'
            dockerfile: infrastructure/docker/api/Dockerfile
            condition: ${{ needs.detect-changes.outputs.api == 'true' }}
          - app: worker-service
            context: 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src'
            dockerfile: infrastructure/docker/worker-service/Dockerfile
            condition: ${{ needs.detect-changes.outputs.worker-service == 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.app }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        if: matrix.condition
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.context }}
          file: ${{ matrix.dockerfile }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

name: Security Scanning

on:
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      scan_type:
        description: 'Type of security scan to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - sast
          - dependency
          - container
          - infrastructure
          - secrets

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Comprehensive security scanning
  security-scan:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run comprehensive security scan
        uses: ./.github/actions/security-scan
        with:
          scan-type: ${{ inputs.scan_type || 'all' }}
          working-directory: '.'
          severity-threshold: 'MEDIUM'
          fail-on-issues: 'false'

      - name: Upload security scan results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-scan-results-${{ github.run_id }}
          path: security-reports/
          retention-days: 30

  # SAST with multiple tools
  sast-scan:
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'sast' || inputs.scan_type == 'all' || github.event_name != 'workflow_dispatch'
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          config: >-
            p/security-audit
            p/secrets
            p/owasp-top-ten
            p/cwe-top-25
            p/r2c-security-audit
          generateSarif: "1"
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: 'csharp,javascript'
          queries: security-and-quality

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Dependency vulnerability scanning
  dependency-scan:
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'dependency' || inputs.scan_type == 'all' || github.event_name != 'workflow_dispatch'
    strategy:
      matrix:
        include:
          - path: 'GenAI PoCs/stencil-residual-app'
            type: 'npm'
          - path: 'GenAI PoCs/stencil-residual-app-api'
            type: 'nuget'
          - path: 'GenAI PoCs/ProcessingApp/VehicleExportWorkerService/src'
            type: 'nuget'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: ${{ matrix.path }}
          format: 'sarif'
          output: 'trivy-${{ matrix.type }}-results.sarif'
          severity: 'CRITICAL,HIGH,MEDIUM'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-${{ matrix.type }}-results.sarif'

      - name: Run npm audit (for Node.js projects)
        if: matrix.type == 'npm'
        working-directory: ${{ matrix.path }}
        run: |
          npm audit --audit-level=moderate --json > npm-audit-results.json || true
          
      - name: Run dotnet list package vulnerabilities (for .NET projects)
        if: matrix.type == 'nuget'
        working-directory: ${{ matrix.path }}
        run: |
          dotnet list package --vulnerable --include-transitive > dotnet-vulnerabilities.txt || true

  # Container security scanning
  container-scan:
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'container' || inputs.scan_type == 'all' || github.event_name != 'workflow_dispatch'
    needs: []
    strategy:
      matrix:
        include:
          - image: 'mcr.microsoft.com/dotnet/aspnet:9.0'
            name: 'api-base'
          - image: 'mcr.microsoft.com/dotnet/runtime:8.0'
            name: 'worker-base'
          - image: 'node:18-alpine'
            name: 'frontend-base'
    steps:
      - name: Run Trivy vulnerability scanner on base images
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ matrix.image }}
          format: 'sarif'
          output: 'trivy-${{ matrix.name }}-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-${{ matrix.name }}-results.sarif'

  # Infrastructure security scanning
  infrastructure-scan:
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'infrastructure' || inputs.scan_type == 'all' || github.event_name != 'workflow_dispatch'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Checkov
        uses: bridgecrewio/checkov-action@master
        with:
          directory: infrastructure/
          framework: terraform,dockerfile,kubernetes
          output_format: sarif
          output_file_path: checkov-results.sarif
          soft_fail: true

      - name: Upload Checkov scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: checkov-results.sarif

      - name: Run tfsec
        uses: aquasecurity/tfsec-sarif-action@v0.1.4
        with:
          sarif_file: tfsec-results.sarif
          working_directory: infrastructure/terraform

      - name: Upload tfsec scan results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: tfsec-results.sarif

  # Secret scanning
  secret-scan:
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'secrets' || inputs.scan_type == 'all' || github.event_name != 'workflow_dispatch'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # License compliance scanning
  license-scan:
    runs-on: ubuntu-latest
    if: inputs.scan_type == 'all' || github.event_name != 'workflow_dispatch'
    strategy:
      matrix:
        include:
          - path: 'GenAI PoCs/stencil-residual-app'
            type: 'npm'
          - path: 'GenAI PoCs/stencil-residual-app-api'
            type: 'nuget'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js (for npm projects)
        if: matrix.type == 'npm'
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Check npm licenses
        if: matrix.type == 'npm'
        working-directory: ${{ matrix.path }}
        run: |
          npm install
          npx license-checker --json --out licenses.json
          npx license-checker --summary

      - name: Setup .NET (for NuGet projects)
        if: matrix.type == 'nuget'
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      - name: Check NuGet licenses
        if: matrix.type == 'nuget'
        working-directory: ${{ matrix.path }}
        run: |
          dotnet restore
          dotnet list package --include-transitive > packages.txt

  # Security report generation
  generate-security-report:
    runs-on: ubuntu-latest
    needs: [security-scan, sast-scan, dependency-scan, container-scan, infrastructure-scan, secret-scan, license-scan]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: security-artifacts

      - name: Generate security summary report
        run: |
          mkdir -p security-summary
          
          cat > security-summary/README.md << 'EOF'
          # Security Scan Summary Report
          
          Generated on: $(date)
          Commit: ${{ github.sha }}
          Branch: ${{ github.ref_name }}
          
          ## Scan Results
          
          ### SAST (Static Application Security Testing)
          - **Tools**: Semgrep, CodeQL
          - **Status**: Check GitHub Security tab for detailed results
          
          ### Dependency Scanning
          - **Tools**: Trivy, npm audit, dotnet vulnerabilities
          - **Status**: Check artifacts for detailed results
          
          ### Container Scanning
          - **Tools**: Trivy
          - **Status**: Base images scanned for vulnerabilities
          
          ### Infrastructure Scanning
          - **Tools**: Checkov, tfsec
          - **Status**: Terraform and Kubernetes configurations scanned
          
          ### Secret Scanning
          - **Tools**: TruffleHog, GitLeaks
          - **Status**: Repository scanned for exposed secrets
          
          ### License Compliance
          - **Tools**: license-checker, dotnet list package
          - **Status**: Dependencies scanned for license compliance
          
          ## Recommendations
          
          1. Review all HIGH and CRITICAL findings
          2. Update vulnerable dependencies
          3. Fix infrastructure misconfigurations
          4. Ensure no secrets are committed to repository
          5. Verify license compliance for all dependencies
          
          ## Next Steps
          
          - Address critical and high-severity findings
          - Update security policies if needed
          - Schedule follow-up scans
          - Update security training materials
          EOF

      - name: Upload security summary
        uses: actions/upload-artifact@v4
        with:
          name: security-summary-report
          path: security-summary/
          retention-days: 90

      - name: Comment on PR (if applicable)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('security-summary/README.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🔒 Security Scan Results\n\n${summary}`
            });

  # Notify security team
  notify-security-team:
    runs-on: ubuntu-latest
    needs: [generate-security-report]
    if: always() && (github.ref == 'refs/heads/main' || github.event_name == 'schedule')
    steps:
      - name: Notify security team
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#security'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            Security scan completed for ${{ github.repository }}
            Branch: ${{ github.ref_name }}
            Commit: ${{ github.sha }}
            
            Please review the security scan results in the GitHub Security tab.

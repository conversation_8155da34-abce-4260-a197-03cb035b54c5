name: Deploy Applications

on:
  workflow_run:
    workflows: ["CI/CD Pipeline"]
    types:
      - completed
    branches: [main, develop, 'release/*']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - production
      force_deploy:
        description: 'Force deployment even if no changes detected'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Determine deployment environment and strategy
  setup-deployment:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      deploy_frontend: ${{ steps.changes.outputs.deploy_frontend }}
      deploy_api: ${{ steps.changes.outputs.deploy_api }}
      deploy_worker: ${{ steps.changes.outputs.deploy_worker }}
      deploy_lambda: ${{ steps.changes.outputs.deploy_lambda }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Determine environment
        id: env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == refs/heads/release/* ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
          else
            echo "environment=dev" >> $GITHUB_OUTPUT
          fi

      - name: Check deployment conditions
        id: changes
        run: |
          # Check if artifacts exist from the CI pipeline
          echo "deploy_frontend=true" >> $GITHUB_OUTPUT
          echo "deploy_api=true" >> $GITHUB_OUTPUT
          echo "deploy_worker=true" >> $GITHUB_OUTPUT
          echo "deploy_lambda=true" >> $GITHUB_OUTPUT

  # Deploy Infrastructure
  deploy-infrastructure:
    runs-on: ubuntu-latest
    needs: setup-deployment
    environment: ${{ needs.setup-deployment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.6.0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION || 'us-east-1' }}

      - name: Terraform Init
        working-directory: infrastructure/terraform/environments/${{ needs.setup-deployment.outputs.environment }}
        run: terraform init

      - name: Terraform Plan
        working-directory: infrastructure/terraform/environments/${{ needs.setup-deployment.outputs.environment }}
        run: terraform plan -var-file="terraform.tfvars"

      - name: Terraform Apply
        if: github.ref == 'refs/heads/main' || inputs.force_deploy
        working-directory: infrastructure/terraform/environments/${{ needs.setup-deployment.outputs.environment }}
        run: terraform apply -auto-approve -var-file="terraform.tfvars"

  # Deploy Frontend Application
  deploy-frontend:
    runs-on: ubuntu-latest
    needs: [setup-deployment, deploy-infrastructure]
    if: needs.setup-deployment.outputs.deploy_frontend == 'true'
    environment: ${{ needs.setup-deployment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download frontend artifacts
        uses: actions/download-artifact@v4
        with:
          name: stencil-app-build
          path: ./dist

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION || 'us-east-1' }}

      - name: Deploy to S3
        run: |
          aws s3 sync ./dist s3://${{ vars.S3_BUCKET_NAME }}-${{ needs.setup-deployment.outputs.environment }} --delete

      - name: Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation --distribution-id ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

  # Deploy API Application
  deploy-api:
    runs-on: ubuntu-latest
    needs: [setup-deployment, deploy-infrastructure]
    if: needs.setup-deployment.outputs.deploy_api == 'true'
    environment: ${{ needs.setup-deployment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION || 'us-east-1' }}

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          aws eks update-kubeconfig --region ${{ vars.AWS_REGION || 'us-east-1' }} --name ${{ vars.EKS_CLUSTER_NAME }}-${{ needs.setup-deployment.outputs.environment }}

      - name: Setup Helm
        uses: azure/setup-helm@v3
        with:
          version: '3.12.0'

      - name: Deploy API with Helm
        run: |
          helm upgrade --install api-${{ needs.setup-deployment.outputs.environment }} \
            ./infrastructure/helm/api \
            --namespace ais-${{ needs.setup-deployment.outputs.environment }} \
            --create-namespace \
            --set image.repository=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/api \
            --set image.tag=${{ github.sha }} \
            --set environment=${{ needs.setup-deployment.outputs.environment }} \
            --set database.connectionString="${{ secrets.DATABASE_CONNECTION_STRING }}" \
            --values ./infrastructure/helm/api/values-${{ needs.setup-deployment.outputs.environment }}.yaml

  # Deploy Worker Service
  deploy-worker-service:
    runs-on: ubuntu-latest
    needs: [setup-deployment, deploy-infrastructure]
    if: needs.setup-deployment.outputs.deploy_worker == 'true'
    environment: ${{ needs.setup-deployment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION || 'us-east-1' }}

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          aws eks update-kubeconfig --region ${{ vars.AWS_REGION || 'us-east-1' }} --name ${{ vars.EKS_CLUSTER_NAME }}-${{ needs.setup-deployment.outputs.environment }}

      - name: Setup Helm
        uses: azure/setup-helm@v3
        with:
          version: '3.12.0'

      - name: Deploy Worker Service with Helm
        run: |
          helm upgrade --install worker-${{ needs.setup-deployment.outputs.environment }} \
            ./infrastructure/helm/worker-service \
            --namespace ais-${{ needs.setup-deployment.outputs.environment }} \
            --create-namespace \
            --set image.repository=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/worker-service \
            --set image.tag=${{ github.sha }} \
            --set environment=${{ needs.setup-deployment.outputs.environment }} \
            --set database.connectionString="${{ secrets.DATABASE_CONNECTION_STRING }}" \
            --set email.smtpServer="${{ secrets.SMTP_SERVER }}" \
            --set email.smtpUser="${{ secrets.SMTP_USER }}" \
            --set email.smtpPassword="${{ secrets.SMTP_PASSWORD }}" \
            --values ./infrastructure/helm/worker-service/values-${{ needs.setup-deployment.outputs.environment }}.yaml

  # Deploy Lambda Function
  deploy-lambda:
    runs-on: ubuntu-latest
    needs: [setup-deployment, deploy-infrastructure]
    if: needs.setup-deployment.outputs.deploy_lambda == 'true'
    environment: ${{ needs.setup-deployment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Lambda package
        uses: actions/download-artifact@v4
        with:
          name: lambda-package
          path: ./lambda

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_REGION || 'us-east-1' }}

      - name: Deploy Lambda function
        run: |
          aws lambda update-function-code \
            --function-name vehicle-export-worker-${{ needs.setup-deployment.outputs.environment }} \
            --zip-file fileb://./lambda/lambda-package.zip

      - name: Update Lambda configuration
        run: |
          aws lambda update-function-configuration \
            --function-name vehicle-export-worker-${{ needs.setup-deployment.outputs.environment }} \
            --environment Variables="{
              ENVIRONMENT=${{ needs.setup-deployment.outputs.environment }},
              DATABASE_CONNECTION_STRING=${{ secrets.DATABASE_CONNECTION_STRING }},
              SMTP_SERVER=${{ secrets.SMTP_SERVER }},
              SMTP_USER=${{ secrets.SMTP_USER }},
              SMTP_PASSWORD=${{ secrets.SMTP_PASSWORD }}
            }"

  # Post-deployment verification
  verify-deployment:
    runs-on: ubuntu-latest
    needs: [setup-deployment, deploy-frontend, deploy-api, deploy-worker-service, deploy-lambda]
    if: always()
    environment: ${{ needs.setup-deployment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run health checks
        run: |
          # API Health Check
          if [[ "${{ needs.deploy-api.result }}" == "success" ]]; then
            echo "Checking API health..."
            curl -f ${{ vars.API_BASE_URL }}/health || exit 1
          fi

          # Frontend Health Check
          if [[ "${{ needs.deploy-frontend.result }}" == "success" ]]; then
            echo "Checking Frontend availability..."
            curl -f ${{ vars.FRONTEND_URL }} || exit 1
          fi

      - name: Run smoke tests
        run: |
          echo "Running smoke tests..."
          # Add your smoke test commands here

      - name: Notify deployment status
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
